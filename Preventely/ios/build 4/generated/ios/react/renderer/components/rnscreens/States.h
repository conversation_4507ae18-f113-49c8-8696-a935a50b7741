/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateStateH.js
 */
#pragma once

#ifdef ANDROID
#include <folly/dynamic.h>
#endif

namespace facebook::react {

class RNSScreenContainerState {
public:
  RNSScreenContainerState() = default;

#ifdef ANDROID
  RNSScreenContainerState(RNSScreenContainerState const &previousState, folly::dynamic data){};
  folly::dynamic getDynamic() const {
    return {};
  };
#endif
};

class RNSScreenContentWrapperState {
public:
  RNSScreenContentWrapperState() = default;

#ifdef ANDROID
  RNSScreenContentWrapperState(RNSScreenContentWrapperState const &previousState, folly::dynamic data){};
  folly::dynamic getDynamic() const {
    return {};
  };
#endif
};

class RNSScreenFooterState {
public:
  RNSScreenFooterState() = default;

#ifdef ANDROID
  RNSScreenFooterState(RNSScreenFooterState const &previousState, folly::dynamic data){};
  folly::dynamic getDynamic() const {
    return {};
  };
#endif
};

class RNSScreenNavigationContainerState {
public:
  RNSScreenNavigationContainerState() = default;

#ifdef ANDROID
  RNSScreenNavigationContainerState(RNSScreenNavigationContainerState const &previousState, folly::dynamic data){};
  folly::dynamic getDynamic() const {
    return {};
  };
#endif
};

class RNSScreenStackState {
public:
  RNSScreenStackState() = default;

#ifdef ANDROID
  RNSScreenStackState(RNSScreenStackState const &previousState, folly::dynamic data){};
  folly::dynamic getDynamic() const {
    return {};
  };
#endif
};

class RNSSearchBarState {
public:
  RNSSearchBarState() = default;

#ifdef ANDROID
  RNSSearchBarState(RNSSearchBarState const &previousState, folly::dynamic data){};
  folly::dynamic getDynamic() const {
    return {};
  };
#endif
};

} // namespace facebook::react