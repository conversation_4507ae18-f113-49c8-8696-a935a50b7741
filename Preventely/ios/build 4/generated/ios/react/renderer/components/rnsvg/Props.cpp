
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GeneratePropsCpp.js
 */

#include <react/renderer/components/rnsvg/Props.h>
#include <folly/dynamic.h>
#include <react/renderer/components/image/conversions.h>
#include <react/renderer/core/PropsParserContext.h>
#include <react/renderer/core/propsConversions.h>

namespace facebook::react {

RNSVGSvgViewAndroidProps::RNSVGSvgViewAndroidProps(
    const PropsParserContext &context,
    const RNSVGSvgViewAndroidProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    bbWidth(convertRawProp(context, rawProps, "bbWidth", sourceProps.bbWidth, {})),
    bbHeight(convertRawProp(context, rawProps, "bbHeight", sourceProps.bbHeight, {})),
    minX(convertRawProp(context, rawProps, "minX", sourceProps.minX, {0.0})),
    minY(convertRawProp(context, rawProps, "minY", sourceProps.minY, {0.0})),
    vbWidth(convertRawProp(context, rawProps, "vbWidth", sourceProps.vbWidth, {0.0})),
    vbHeight(convertRawProp(context, rawProps, "vbHeight", sourceProps.vbHeight, {0.0})),
    align(convertRawProp(context, rawProps, "align", sourceProps.align, {})),
    meetOrSlice(convertRawProp(context, rawProps, "meetOrSlice", sourceProps.meetOrSlice, {0})),
    color(convertRawProp(context, rawProps, "color", sourceProps.color, {})),
    pointerEvents(convertRawProp(context, rawProps, "pointerEvents", sourceProps.pointerEvents, {})),
    hasTVPreferredFocus(convertRawProp(context, rawProps, "hasTVPreferredFocus", sourceProps.hasTVPreferredFocus, {false})),
    borderBottomColor(convertRawProp(context, rawProps, "borderBottomColor", sourceProps.borderBottomColor, {})),
    nextFocusDown(convertRawProp(context, rawProps, "nextFocusDown", sourceProps.nextFocusDown, {0})),
    borderRightColor(convertRawProp(context, rawProps, "borderRightColor", sourceProps.borderRightColor, {})),
    nextFocusRight(convertRawProp(context, rawProps, "nextFocusRight", sourceProps.nextFocusRight, {0})),
    borderLeftColor(convertRawProp(context, rawProps, "borderLeftColor", sourceProps.borderLeftColor, {})),
    borderColor(convertRawProp(context, rawProps, "borderColor", sourceProps.borderColor, {})),
    removeClippedSubviews(convertRawProp(context, rawProps, "removeClippedSubviews", sourceProps.removeClippedSubviews, {false})),
    nextFocusForward(convertRawProp(context, rawProps, "nextFocusForward", sourceProps.nextFocusForward, {0})),
    nextFocusUp(convertRawProp(context, rawProps, "nextFocusUp", sourceProps.nextFocusUp, {0})),
    accessible(convertRawProp(context, rawProps, "accessible", sourceProps.accessible, {false})),
    borderStartColor(convertRawProp(context, rawProps, "borderStartColor", sourceProps.borderStartColor, {})),
    borderEndColor(convertRawProp(context, rawProps, "borderEndColor", sourceProps.borderEndColor, {})),
    focusable(convertRawProp(context, rawProps, "focusable", sourceProps.focusable, {false})),
    nativeBackgroundAndroid(convertRawProp(context, rawProps, "nativeBackgroundAndroid", sourceProps.nativeBackgroundAndroid, {})),
    nativeForegroundAndroid(convertRawProp(context, rawProps, "nativeForegroundAndroid", sourceProps.nativeForegroundAndroid, {})),
    backfaceVisibility(convertRawProp(context, rawProps, "backfaceVisibility", sourceProps.backfaceVisibility, {})),
    borderStyle(convertRawProp(context, rawProps, "borderStyle", sourceProps.borderStyle, {})),
    needsOffscreenAlphaCompositing(convertRawProp(context, rawProps, "needsOffscreenAlphaCompositing", sourceProps.needsOffscreenAlphaCompositing, {false})),
    hitSlop(convertRawProp(context, rawProps, "hitSlop", sourceProps.hitSlop, {})),
    borderTopColor(convertRawProp(context, rawProps, "borderTopColor", sourceProps.borderTopColor, {})),
    nextFocusLeft(convertRawProp(context, rawProps, "nextFocusLeft", sourceProps.nextFocusLeft, {0})),
    borderBlockColor(convertRawProp(context, rawProps, "borderBlockColor", sourceProps.borderBlockColor, {})),
    borderBlockEndColor(convertRawProp(context, rawProps, "borderBlockEndColor", sourceProps.borderBlockEndColor, {})),
    borderBlockStartColor(convertRawProp(context, rawProps, "borderBlockStartColor", sourceProps.borderBlockStartColor, {})),
    borderRadius(convertRawProp(context, rawProps, "borderRadius", sourceProps.borderRadius, {})),
    borderTopLeftRadius(convertRawProp(context, rawProps, "borderTopLeftRadius", sourceProps.borderTopLeftRadius, {})),
    borderTopRightRadius(convertRawProp(context, rawProps, "borderTopRightRadius", sourceProps.borderTopRightRadius, {})),
    borderBottomRightRadius(convertRawProp(context, rawProps, "borderBottomRightRadius", sourceProps.borderBottomRightRadius, {})),
    borderBottomLeftRadius(convertRawProp(context, rawProps, "borderBottomLeftRadius", sourceProps.borderBottomLeftRadius, {})),
    borderTopStartRadius(convertRawProp(context, rawProps, "borderTopStartRadius", sourceProps.borderTopStartRadius, {})),
    borderTopEndRadius(convertRawProp(context, rawProps, "borderTopEndRadius", sourceProps.borderTopEndRadius, {})),
    borderBottomStartRadius(convertRawProp(context, rawProps, "borderBottomStartRadius", sourceProps.borderBottomStartRadius, {})),
    borderBottomEndRadius(convertRawProp(context, rawProps, "borderBottomEndRadius", sourceProps.borderBottomEndRadius, {})),
    borderEndEndRadius(convertRawProp(context, rawProps, "borderEndEndRadius", sourceProps.borderEndEndRadius, {})),
    borderEndStartRadius(convertRawProp(context, rawProps, "borderEndStartRadius", sourceProps.borderEndStartRadius, {})),
    borderStartEndRadius(convertRawProp(context, rawProps, "borderStartEndRadius", sourceProps.borderStartEndRadius, {})),
    borderStartStartRadius(convertRawProp(context, rawProps, "borderStartStartRadius", sourceProps.borderStartStartRadius, {}))
      {}
RNSVGCircleProps::RNSVGCircleProps(
    const PropsParserContext &context,
    const RNSVGCircleProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    name(convertRawProp(context, rawProps, "name", sourceProps.name, {})),
    opacity(convertRawProp(context, rawProps, "opacity", sourceProps.opacity, {1.0})),
    matrix(convertRawProp(context, rawProps, "matrix", sourceProps.matrix, {})),
    mask(convertRawProp(context, rawProps, "mask", sourceProps.mask, {})),
    markerStart(convertRawProp(context, rawProps, "markerStart", sourceProps.markerStart, {})),
    markerMid(convertRawProp(context, rawProps, "markerMid", sourceProps.markerMid, {})),
    markerEnd(convertRawProp(context, rawProps, "markerEnd", sourceProps.markerEnd, {})),
    clipPath(convertRawProp(context, rawProps, "clipPath", sourceProps.clipPath, {})),
    clipRule(convertRawProp(context, rawProps, "clipRule", sourceProps.clipRule, {0})),
    responsible(convertRawProp(context, rawProps, "responsible", sourceProps.responsible, {false})),
    display(convertRawProp(context, rawProps, "display", sourceProps.display, {})),
    pointerEvents(convertRawProp(context, rawProps, "pointerEvents", sourceProps.pointerEvents, {})),
    color(convertRawProp(context, rawProps, "color", sourceProps.color, {})),
    fill(convertRawProp(context, rawProps, "fill", sourceProps.fill, {})),
    fillOpacity(convertRawProp(context, rawProps, "fillOpacity", sourceProps.fillOpacity, {1.0})),
    fillRule(convertRawProp(context, rawProps, "fillRule", sourceProps.fillRule, {1})),
    stroke(convertRawProp(context, rawProps, "stroke", sourceProps.stroke, {})),
    strokeOpacity(convertRawProp(context, rawProps, "strokeOpacity", sourceProps.strokeOpacity, {1.0})),
    strokeWidth(convertRawProp(context, rawProps, "strokeWidth", sourceProps.strokeWidth, {})),
    strokeLinecap(convertRawProp(context, rawProps, "strokeLinecap", sourceProps.strokeLinecap, {0})),
    strokeLinejoin(convertRawProp(context, rawProps, "strokeLinejoin", sourceProps.strokeLinejoin, {0})),
    strokeDasharray(convertRawProp(context, rawProps, "strokeDasharray", sourceProps.strokeDasharray, {})),
    strokeDashoffset(convertRawProp(context, rawProps, "strokeDashoffset", sourceProps.strokeDashoffset, {0.0})),
    strokeMiterlimit(convertRawProp(context, rawProps, "strokeMiterlimit", sourceProps.strokeMiterlimit, {0.0})),
    vectorEffect(convertRawProp(context, rawProps, "vectorEffect", sourceProps.vectorEffect, {0})),
    propList(convertRawProp(context, rawProps, "propList", sourceProps.propList, {})),
    filter(convertRawProp(context, rawProps, "filter", sourceProps.filter, {})),
    cx(convertRawProp(context, rawProps, "cx", sourceProps.cx, {})),
    cy(convertRawProp(context, rawProps, "cy", sourceProps.cy, {})),
    r(convertRawProp(context, rawProps, "r", sourceProps.r, {}))
      {}
RNSVGClipPathProps::RNSVGClipPathProps(
    const PropsParserContext &context,
    const RNSVGClipPathProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    name(convertRawProp(context, rawProps, "name", sourceProps.name, {})),
    opacity(convertRawProp(context, rawProps, "opacity", sourceProps.opacity, {1.0})),
    matrix(convertRawProp(context, rawProps, "matrix", sourceProps.matrix, {})),
    mask(convertRawProp(context, rawProps, "mask", sourceProps.mask, {})),
    markerStart(convertRawProp(context, rawProps, "markerStart", sourceProps.markerStart, {})),
    markerMid(convertRawProp(context, rawProps, "markerMid", sourceProps.markerMid, {})),
    markerEnd(convertRawProp(context, rawProps, "markerEnd", sourceProps.markerEnd, {})),
    clipPath(convertRawProp(context, rawProps, "clipPath", sourceProps.clipPath, {})),
    clipRule(convertRawProp(context, rawProps, "clipRule", sourceProps.clipRule, {0})),
    responsible(convertRawProp(context, rawProps, "responsible", sourceProps.responsible, {false})),
    display(convertRawProp(context, rawProps, "display", sourceProps.display, {})),
    pointerEvents(convertRawProp(context, rawProps, "pointerEvents", sourceProps.pointerEvents, {})),
    color(convertRawProp(context, rawProps, "color", sourceProps.color, {})),
    fill(convertRawProp(context, rawProps, "fill", sourceProps.fill, {})),
    fillOpacity(convertRawProp(context, rawProps, "fillOpacity", sourceProps.fillOpacity, {1.0})),
    fillRule(convertRawProp(context, rawProps, "fillRule", sourceProps.fillRule, {1})),
    stroke(convertRawProp(context, rawProps, "stroke", sourceProps.stroke, {})),
    strokeOpacity(convertRawProp(context, rawProps, "strokeOpacity", sourceProps.strokeOpacity, {1.0})),
    strokeWidth(convertRawProp(context, rawProps, "strokeWidth", sourceProps.strokeWidth, {})),
    strokeLinecap(convertRawProp(context, rawProps, "strokeLinecap", sourceProps.strokeLinecap, {0})),
    strokeLinejoin(convertRawProp(context, rawProps, "strokeLinejoin", sourceProps.strokeLinejoin, {0})),
    strokeDasharray(convertRawProp(context, rawProps, "strokeDasharray", sourceProps.strokeDasharray, {})),
    strokeDashoffset(convertRawProp(context, rawProps, "strokeDashoffset", sourceProps.strokeDashoffset, {0.0})),
    strokeMiterlimit(convertRawProp(context, rawProps, "strokeMiterlimit", sourceProps.strokeMiterlimit, {0.0})),
    vectorEffect(convertRawProp(context, rawProps, "vectorEffect", sourceProps.vectorEffect, {0})),
    propList(convertRawProp(context, rawProps, "propList", sourceProps.propList, {})),
    filter(convertRawProp(context, rawProps, "filter", sourceProps.filter, {})),
    fontSize(convertRawProp(context, rawProps, "fontSize", sourceProps.fontSize, {})),
    fontWeight(convertRawProp(context, rawProps, "fontWeight", sourceProps.fontWeight, {})),
    font(convertRawProp(context, rawProps, "font", sourceProps.font, {}))
      {}
RNSVGDefsProps::RNSVGDefsProps(
    const PropsParserContext &context,
    const RNSVGDefsProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    name(convertRawProp(context, rawProps, "name", sourceProps.name, {})),
    opacity(convertRawProp(context, rawProps, "opacity", sourceProps.opacity, {1.0})),
    matrix(convertRawProp(context, rawProps, "matrix", sourceProps.matrix, {})),
    mask(convertRawProp(context, rawProps, "mask", sourceProps.mask, {})),
    markerStart(convertRawProp(context, rawProps, "markerStart", sourceProps.markerStart, {})),
    markerMid(convertRawProp(context, rawProps, "markerMid", sourceProps.markerMid, {})),
    markerEnd(convertRawProp(context, rawProps, "markerEnd", sourceProps.markerEnd, {})),
    clipPath(convertRawProp(context, rawProps, "clipPath", sourceProps.clipPath, {})),
    clipRule(convertRawProp(context, rawProps, "clipRule", sourceProps.clipRule, {0})),
    responsible(convertRawProp(context, rawProps, "responsible", sourceProps.responsible, {false})),
    display(convertRawProp(context, rawProps, "display", sourceProps.display, {})),
    pointerEvents(convertRawProp(context, rawProps, "pointerEvents", sourceProps.pointerEvents, {}))
      {}
RNSVGEllipseProps::RNSVGEllipseProps(
    const PropsParserContext &context,
    const RNSVGEllipseProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    name(convertRawProp(context, rawProps, "name", sourceProps.name, {})),
    opacity(convertRawProp(context, rawProps, "opacity", sourceProps.opacity, {1.0})),
    matrix(convertRawProp(context, rawProps, "matrix", sourceProps.matrix, {})),
    mask(convertRawProp(context, rawProps, "mask", sourceProps.mask, {})),
    markerStart(convertRawProp(context, rawProps, "markerStart", sourceProps.markerStart, {})),
    markerMid(convertRawProp(context, rawProps, "markerMid", sourceProps.markerMid, {})),
    markerEnd(convertRawProp(context, rawProps, "markerEnd", sourceProps.markerEnd, {})),
    clipPath(convertRawProp(context, rawProps, "clipPath", sourceProps.clipPath, {})),
    clipRule(convertRawProp(context, rawProps, "clipRule", sourceProps.clipRule, {0})),
    responsible(convertRawProp(context, rawProps, "responsible", sourceProps.responsible, {false})),
    display(convertRawProp(context, rawProps, "display", sourceProps.display, {})),
    pointerEvents(convertRawProp(context, rawProps, "pointerEvents", sourceProps.pointerEvents, {})),
    color(convertRawProp(context, rawProps, "color", sourceProps.color, {})),
    fill(convertRawProp(context, rawProps, "fill", sourceProps.fill, {})),
    fillOpacity(convertRawProp(context, rawProps, "fillOpacity", sourceProps.fillOpacity, {1.0})),
    fillRule(convertRawProp(context, rawProps, "fillRule", sourceProps.fillRule, {1})),
    stroke(convertRawProp(context, rawProps, "stroke", sourceProps.stroke, {})),
    strokeOpacity(convertRawProp(context, rawProps, "strokeOpacity", sourceProps.strokeOpacity, {1.0})),
    strokeWidth(convertRawProp(context, rawProps, "strokeWidth", sourceProps.strokeWidth, {})),
    strokeLinecap(convertRawProp(context, rawProps, "strokeLinecap", sourceProps.strokeLinecap, {0})),
    strokeLinejoin(convertRawProp(context, rawProps, "strokeLinejoin", sourceProps.strokeLinejoin, {0})),
    strokeDasharray(convertRawProp(context, rawProps, "strokeDasharray", sourceProps.strokeDasharray, {})),
    strokeDashoffset(convertRawProp(context, rawProps, "strokeDashoffset", sourceProps.strokeDashoffset, {0.0})),
    strokeMiterlimit(convertRawProp(context, rawProps, "strokeMiterlimit", sourceProps.strokeMiterlimit, {0.0})),
    vectorEffect(convertRawProp(context, rawProps, "vectorEffect", sourceProps.vectorEffect, {0})),
    propList(convertRawProp(context, rawProps, "propList", sourceProps.propList, {})),
    filter(convertRawProp(context, rawProps, "filter", sourceProps.filter, {})),
    cx(convertRawProp(context, rawProps, "cx", sourceProps.cx, {})),
    cy(convertRawProp(context, rawProps, "cy", sourceProps.cy, {})),
    rx(convertRawProp(context, rawProps, "rx", sourceProps.rx, {})),
    ry(convertRawProp(context, rawProps, "ry", sourceProps.ry, {}))
      {}
RNSVGFeBlendProps::RNSVGFeBlendProps(
    const PropsParserContext &context,
    const RNSVGFeBlendProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    x(convertRawProp(context, rawProps, "x", sourceProps.x, {})),
    y(convertRawProp(context, rawProps, "y", sourceProps.y, {})),
    width(convertRawProp(context, rawProps, "width", sourceProps.width, {})),
    height(convertRawProp(context, rawProps, "height", sourceProps.height, {})),
    result(convertRawProp(context, rawProps, "result", sourceProps.result, {})),
    in1(convertRawProp(context, rawProps, "in1", sourceProps.in1, {})),
    in2(convertRawProp(context, rawProps, "in2", sourceProps.in2, {})),
    mode(convertRawProp(context, rawProps, "mode", sourceProps.mode, {RNSVGFeBlendMode::Normal}))
      {}
RNSVGFeColorMatrixProps::RNSVGFeColorMatrixProps(
    const PropsParserContext &context,
    const RNSVGFeColorMatrixProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    x(convertRawProp(context, rawProps, "x", sourceProps.x, {})),
    y(convertRawProp(context, rawProps, "y", sourceProps.y, {})),
    width(convertRawProp(context, rawProps, "width", sourceProps.width, {})),
    height(convertRawProp(context, rawProps, "height", sourceProps.height, {})),
    result(convertRawProp(context, rawProps, "result", sourceProps.result, {})),
    in1(convertRawProp(context, rawProps, "in1", sourceProps.in1, {})),
    type(convertRawProp(context, rawProps, "type", sourceProps.type, {RNSVGFeColorMatrixType::Matrix})),
    values(convertRawProp(context, rawProps, "values", sourceProps.values, {}))
      {}
RNSVGFeCompositeProps::RNSVGFeCompositeProps(
    const PropsParserContext &context,
    const RNSVGFeCompositeProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    x(convertRawProp(context, rawProps, "x", sourceProps.x, {})),
    y(convertRawProp(context, rawProps, "y", sourceProps.y, {})),
    width(convertRawProp(context, rawProps, "width", sourceProps.width, {})),
    height(convertRawProp(context, rawProps, "height", sourceProps.height, {})),
    result(convertRawProp(context, rawProps, "result", sourceProps.result, {})),
    in1(convertRawProp(context, rawProps, "in1", sourceProps.in1, {})),
    in2(convertRawProp(context, rawProps, "in2", sourceProps.in2, {})),
    operator1(convertRawProp(context, rawProps, "operator1", sourceProps.operator1, {RNSVGFeCompositeOperator1::Over})),
    k1(convertRawProp(context, rawProps, "k1", sourceProps.k1, {0.0})),
    k2(convertRawProp(context, rawProps, "k2", sourceProps.k2, {0.0})),
    k3(convertRawProp(context, rawProps, "k3", sourceProps.k3, {0.0})),
    k4(convertRawProp(context, rawProps, "k4", sourceProps.k4, {0.0}))
      {}
RNSVGFeFloodProps::RNSVGFeFloodProps(
    const PropsParserContext &context,
    const RNSVGFeFloodProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    x(convertRawProp(context, rawProps, "x", sourceProps.x, {})),
    y(convertRawProp(context, rawProps, "y", sourceProps.y, {})),
    width(convertRawProp(context, rawProps, "width", sourceProps.width, {})),
    height(convertRawProp(context, rawProps, "height", sourceProps.height, {})),
    result(convertRawProp(context, rawProps, "result", sourceProps.result, {})),
    floodColor(convertRawProp(context, rawProps, "floodColor", sourceProps.floodColor, {})),
    floodOpacity(convertRawProp(context, rawProps, "floodOpacity", sourceProps.floodOpacity, {1.0}))
      {}
RNSVGFeGaussianBlurProps::RNSVGFeGaussianBlurProps(
    const PropsParserContext &context,
    const RNSVGFeGaussianBlurProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    x(convertRawProp(context, rawProps, "x", sourceProps.x, {})),
    y(convertRawProp(context, rawProps, "y", sourceProps.y, {})),
    width(convertRawProp(context, rawProps, "width", sourceProps.width, {})),
    height(convertRawProp(context, rawProps, "height", sourceProps.height, {})),
    result(convertRawProp(context, rawProps, "result", sourceProps.result, {})),
    in1(convertRawProp(context, rawProps, "in1", sourceProps.in1, {})),
    stdDeviationX(convertRawProp(context, rawProps, "stdDeviationX", sourceProps.stdDeviationX, {0.0})),
    stdDeviationY(convertRawProp(context, rawProps, "stdDeviationY", sourceProps.stdDeviationY, {0.0})),
    edgeMode(convertRawProp(context, rawProps, "edgeMode", sourceProps.edgeMode, {RNSVGFeGaussianBlurEdgeMode::None}))
      {}
RNSVGFeMergeProps::RNSVGFeMergeProps(
    const PropsParserContext &context,
    const RNSVGFeMergeProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    x(convertRawProp(context, rawProps, "x", sourceProps.x, {})),
    y(convertRawProp(context, rawProps, "y", sourceProps.y, {})),
    width(convertRawProp(context, rawProps, "width", sourceProps.width, {})),
    height(convertRawProp(context, rawProps, "height", sourceProps.height, {})),
    result(convertRawProp(context, rawProps, "result", sourceProps.result, {})),
    nodes(convertRawProp(context, rawProps, "nodes", sourceProps.nodes, {}))
      {}
RNSVGFeOffsetProps::RNSVGFeOffsetProps(
    const PropsParserContext &context,
    const RNSVGFeOffsetProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    x(convertRawProp(context, rawProps, "x", sourceProps.x, {})),
    y(convertRawProp(context, rawProps, "y", sourceProps.y, {})),
    width(convertRawProp(context, rawProps, "width", sourceProps.width, {})),
    height(convertRawProp(context, rawProps, "height", sourceProps.height, {})),
    result(convertRawProp(context, rawProps, "result", sourceProps.result, {})),
    in1(convertRawProp(context, rawProps, "in1", sourceProps.in1, {})),
    dx(convertRawProp(context, rawProps, "dx", sourceProps.dx, {})),
    dy(convertRawProp(context, rawProps, "dy", sourceProps.dy, {}))
      {}
RNSVGFilterProps::RNSVGFilterProps(
    const PropsParserContext &context,
    const RNSVGFilterProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    name(convertRawProp(context, rawProps, "name", sourceProps.name, {})),
    x(convertRawProp(context, rawProps, "x", sourceProps.x, {})),
    y(convertRawProp(context, rawProps, "y", sourceProps.y, {})),
    height(convertRawProp(context, rawProps, "height", sourceProps.height, {})),
    width(convertRawProp(context, rawProps, "width", sourceProps.width, {})),
    filterUnits(convertRawProp(context, rawProps, "filterUnits", sourceProps.filterUnits, {RNSVGFilterFilterUnits::ObjectBoundingBox})),
    primitiveUnits(convertRawProp(context, rawProps, "primitiveUnits", sourceProps.primitiveUnits, {RNSVGFilterPrimitiveUnits::UserSpaceOnUse}))
      {}
RNSVGForeignObjectProps::RNSVGForeignObjectProps(
    const PropsParserContext &context,
    const RNSVGForeignObjectProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    name(convertRawProp(context, rawProps, "name", sourceProps.name, {})),
    opacity(convertRawProp(context, rawProps, "opacity", sourceProps.opacity, {1.0})),
    matrix(convertRawProp(context, rawProps, "matrix", sourceProps.matrix, {})),
    mask(convertRawProp(context, rawProps, "mask", sourceProps.mask, {})),
    markerStart(convertRawProp(context, rawProps, "markerStart", sourceProps.markerStart, {})),
    markerMid(convertRawProp(context, rawProps, "markerMid", sourceProps.markerMid, {})),
    markerEnd(convertRawProp(context, rawProps, "markerEnd", sourceProps.markerEnd, {})),
    clipPath(convertRawProp(context, rawProps, "clipPath", sourceProps.clipPath, {})),
    clipRule(convertRawProp(context, rawProps, "clipRule", sourceProps.clipRule, {0})),
    responsible(convertRawProp(context, rawProps, "responsible", sourceProps.responsible, {false})),
    display(convertRawProp(context, rawProps, "display", sourceProps.display, {})),
    pointerEvents(convertRawProp(context, rawProps, "pointerEvents", sourceProps.pointerEvents, {})),
    color(convertRawProp(context, rawProps, "color", sourceProps.color, {})),
    fill(convertRawProp(context, rawProps, "fill", sourceProps.fill, {})),
    fillOpacity(convertRawProp(context, rawProps, "fillOpacity", sourceProps.fillOpacity, {1.0})),
    fillRule(convertRawProp(context, rawProps, "fillRule", sourceProps.fillRule, {1})),
    stroke(convertRawProp(context, rawProps, "stroke", sourceProps.stroke, {})),
    strokeOpacity(convertRawProp(context, rawProps, "strokeOpacity", sourceProps.strokeOpacity, {1.0})),
    strokeWidth(convertRawProp(context, rawProps, "strokeWidth", sourceProps.strokeWidth, {})),
    strokeLinecap(convertRawProp(context, rawProps, "strokeLinecap", sourceProps.strokeLinecap, {0})),
    strokeLinejoin(convertRawProp(context, rawProps, "strokeLinejoin", sourceProps.strokeLinejoin, {0})),
    strokeDasharray(convertRawProp(context, rawProps, "strokeDasharray", sourceProps.strokeDasharray, {})),
    strokeDashoffset(convertRawProp(context, rawProps, "strokeDashoffset", sourceProps.strokeDashoffset, {0.0})),
    strokeMiterlimit(convertRawProp(context, rawProps, "strokeMiterlimit", sourceProps.strokeMiterlimit, {0.0})),
    vectorEffect(convertRawProp(context, rawProps, "vectorEffect", sourceProps.vectorEffect, {0})),
    propList(convertRawProp(context, rawProps, "propList", sourceProps.propList, {})),
    filter(convertRawProp(context, rawProps, "filter", sourceProps.filter, {})),
    fontSize(convertRawProp(context, rawProps, "fontSize", sourceProps.fontSize, {})),
    fontWeight(convertRawProp(context, rawProps, "fontWeight", sourceProps.fontWeight, {})),
    font(convertRawProp(context, rawProps, "font", sourceProps.font, {})),
    x(convertRawProp(context, rawProps, "x", sourceProps.x, {})),
    y(convertRawProp(context, rawProps, "y", sourceProps.y, {})),
    height(convertRawProp(context, rawProps, "height", sourceProps.height, {})),
    width(convertRawProp(context, rawProps, "width", sourceProps.width, {}))
      {}
RNSVGGroupProps::RNSVGGroupProps(
    const PropsParserContext &context,
    const RNSVGGroupProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    name(convertRawProp(context, rawProps, "name", sourceProps.name, {})),
    opacity(convertRawProp(context, rawProps, "opacity", sourceProps.opacity, {1.0})),
    matrix(convertRawProp(context, rawProps, "matrix", sourceProps.matrix, {})),
    mask(convertRawProp(context, rawProps, "mask", sourceProps.mask, {})),
    markerStart(convertRawProp(context, rawProps, "markerStart", sourceProps.markerStart, {})),
    markerMid(convertRawProp(context, rawProps, "markerMid", sourceProps.markerMid, {})),
    markerEnd(convertRawProp(context, rawProps, "markerEnd", sourceProps.markerEnd, {})),
    clipPath(convertRawProp(context, rawProps, "clipPath", sourceProps.clipPath, {})),
    clipRule(convertRawProp(context, rawProps, "clipRule", sourceProps.clipRule, {0})),
    responsible(convertRawProp(context, rawProps, "responsible", sourceProps.responsible, {false})),
    display(convertRawProp(context, rawProps, "display", sourceProps.display, {})),
    pointerEvents(convertRawProp(context, rawProps, "pointerEvents", sourceProps.pointerEvents, {})),
    color(convertRawProp(context, rawProps, "color", sourceProps.color, {})),
    fill(convertRawProp(context, rawProps, "fill", sourceProps.fill, {})),
    fillOpacity(convertRawProp(context, rawProps, "fillOpacity", sourceProps.fillOpacity, {1.0})),
    fillRule(convertRawProp(context, rawProps, "fillRule", sourceProps.fillRule, {1})),
    stroke(convertRawProp(context, rawProps, "stroke", sourceProps.stroke, {})),
    strokeOpacity(convertRawProp(context, rawProps, "strokeOpacity", sourceProps.strokeOpacity, {1.0})),
    strokeWidth(convertRawProp(context, rawProps, "strokeWidth", sourceProps.strokeWidth, {})),
    strokeLinecap(convertRawProp(context, rawProps, "strokeLinecap", sourceProps.strokeLinecap, {0})),
    strokeLinejoin(convertRawProp(context, rawProps, "strokeLinejoin", sourceProps.strokeLinejoin, {0})),
    strokeDasharray(convertRawProp(context, rawProps, "strokeDasharray", sourceProps.strokeDasharray, {})),
    strokeDashoffset(convertRawProp(context, rawProps, "strokeDashoffset", sourceProps.strokeDashoffset, {0.0})),
    strokeMiterlimit(convertRawProp(context, rawProps, "strokeMiterlimit", sourceProps.strokeMiterlimit, {0.0})),
    vectorEffect(convertRawProp(context, rawProps, "vectorEffect", sourceProps.vectorEffect, {0})),
    propList(convertRawProp(context, rawProps, "propList", sourceProps.propList, {})),
    filter(convertRawProp(context, rawProps, "filter", sourceProps.filter, {})),
    fontSize(convertRawProp(context, rawProps, "fontSize", sourceProps.fontSize, {})),
    fontWeight(convertRawProp(context, rawProps, "fontWeight", sourceProps.fontWeight, {})),
    font(convertRawProp(context, rawProps, "font", sourceProps.font, {}))
      {}
RNSVGImageProps::RNSVGImageProps(
    const PropsParserContext &context,
    const RNSVGImageProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    name(convertRawProp(context, rawProps, "name", sourceProps.name, {})),
    opacity(convertRawProp(context, rawProps, "opacity", sourceProps.opacity, {1.0})),
    matrix(convertRawProp(context, rawProps, "matrix", sourceProps.matrix, {})),
    mask(convertRawProp(context, rawProps, "mask", sourceProps.mask, {})),
    markerStart(convertRawProp(context, rawProps, "markerStart", sourceProps.markerStart, {})),
    markerMid(convertRawProp(context, rawProps, "markerMid", sourceProps.markerMid, {})),
    markerEnd(convertRawProp(context, rawProps, "markerEnd", sourceProps.markerEnd, {})),
    clipPath(convertRawProp(context, rawProps, "clipPath", sourceProps.clipPath, {})),
    clipRule(convertRawProp(context, rawProps, "clipRule", sourceProps.clipRule, {0})),
    responsible(convertRawProp(context, rawProps, "responsible", sourceProps.responsible, {false})),
    display(convertRawProp(context, rawProps, "display", sourceProps.display, {})),
    pointerEvents(convertRawProp(context, rawProps, "pointerEvents", sourceProps.pointerEvents, {})),
    color(convertRawProp(context, rawProps, "color", sourceProps.color, {})),
    fill(convertRawProp(context, rawProps, "fill", sourceProps.fill, {})),
    fillOpacity(convertRawProp(context, rawProps, "fillOpacity", sourceProps.fillOpacity, {1.0})),
    fillRule(convertRawProp(context, rawProps, "fillRule", sourceProps.fillRule, {1})),
    stroke(convertRawProp(context, rawProps, "stroke", sourceProps.stroke, {})),
    strokeOpacity(convertRawProp(context, rawProps, "strokeOpacity", sourceProps.strokeOpacity, {1.0})),
    strokeWidth(convertRawProp(context, rawProps, "strokeWidth", sourceProps.strokeWidth, {})),
    strokeLinecap(convertRawProp(context, rawProps, "strokeLinecap", sourceProps.strokeLinecap, {0})),
    strokeLinejoin(convertRawProp(context, rawProps, "strokeLinejoin", sourceProps.strokeLinejoin, {0})),
    strokeDasharray(convertRawProp(context, rawProps, "strokeDasharray", sourceProps.strokeDasharray, {})),
    strokeDashoffset(convertRawProp(context, rawProps, "strokeDashoffset", sourceProps.strokeDashoffset, {0.0})),
    strokeMiterlimit(convertRawProp(context, rawProps, "strokeMiterlimit", sourceProps.strokeMiterlimit, {0.0})),
    vectorEffect(convertRawProp(context, rawProps, "vectorEffect", sourceProps.vectorEffect, {0})),
    propList(convertRawProp(context, rawProps, "propList", sourceProps.propList, {})),
    filter(convertRawProp(context, rawProps, "filter", sourceProps.filter, {})),
    x(convertRawProp(context, rawProps, "x", sourceProps.x, {})),
    y(convertRawProp(context, rawProps, "y", sourceProps.y, {})),
    width(convertRawProp(context, rawProps, "width", sourceProps.width, {})),
    height(convertRawProp(context, rawProps, "height", sourceProps.height, {})),
    src(convertRawProp(context, rawProps, "src", sourceProps.src, {})),
    align(convertRawProp(context, rawProps, "align", sourceProps.align, {})),
    meetOrSlice(convertRawProp(context, rawProps, "meetOrSlice", sourceProps.meetOrSlice, {0}))
      {}
RNSVGSvgViewProps::RNSVGSvgViewProps(
    const PropsParserContext &context,
    const RNSVGSvgViewProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    bbWidth(convertRawProp(context, rawProps, "bbWidth", sourceProps.bbWidth, {})),
    bbHeight(convertRawProp(context, rawProps, "bbHeight", sourceProps.bbHeight, {})),
    minX(convertRawProp(context, rawProps, "minX", sourceProps.minX, {0.0})),
    minY(convertRawProp(context, rawProps, "minY", sourceProps.minY, {0.0})),
    vbWidth(convertRawProp(context, rawProps, "vbWidth", sourceProps.vbWidth, {0.0})),
    vbHeight(convertRawProp(context, rawProps, "vbHeight", sourceProps.vbHeight, {0.0})),
    align(convertRawProp(context, rawProps, "align", sourceProps.align, {})),
    meetOrSlice(convertRawProp(context, rawProps, "meetOrSlice", sourceProps.meetOrSlice, {0})),
    color(convertRawProp(context, rawProps, "color", sourceProps.color, {})),
    pointerEvents(convertRawProp(context, rawProps, "pointerEvents", sourceProps.pointerEvents, {})),
    hitSlop(convertRawProp(context, rawProps, "hitSlop", sourceProps.hitSlop, {}))
      {}
RNSVGLinearGradientProps::RNSVGLinearGradientProps(
    const PropsParserContext &context,
    const RNSVGLinearGradientProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    name(convertRawProp(context, rawProps, "name", sourceProps.name, {})),
    opacity(convertRawProp(context, rawProps, "opacity", sourceProps.opacity, {1.0})),
    matrix(convertRawProp(context, rawProps, "matrix", sourceProps.matrix, {})),
    mask(convertRawProp(context, rawProps, "mask", sourceProps.mask, {})),
    markerStart(convertRawProp(context, rawProps, "markerStart", sourceProps.markerStart, {})),
    markerMid(convertRawProp(context, rawProps, "markerMid", sourceProps.markerMid, {})),
    markerEnd(convertRawProp(context, rawProps, "markerEnd", sourceProps.markerEnd, {})),
    clipPath(convertRawProp(context, rawProps, "clipPath", sourceProps.clipPath, {})),
    clipRule(convertRawProp(context, rawProps, "clipRule", sourceProps.clipRule, {0})),
    responsible(convertRawProp(context, rawProps, "responsible", sourceProps.responsible, {false})),
    display(convertRawProp(context, rawProps, "display", sourceProps.display, {})),
    pointerEvents(convertRawProp(context, rawProps, "pointerEvents", sourceProps.pointerEvents, {})),
    x1(convertRawProp(context, rawProps, "x1", sourceProps.x1, {})),
    y1(convertRawProp(context, rawProps, "y1", sourceProps.y1, {})),
    x2(convertRawProp(context, rawProps, "x2", sourceProps.x2, {})),
    y2(convertRawProp(context, rawProps, "y2", sourceProps.y2, {})),
    gradient(convertRawProp(context, rawProps, "gradient", sourceProps.gradient, {})),
    gradientUnits(convertRawProp(context, rawProps, "gradientUnits", sourceProps.gradientUnits, {0})),
    gradientTransform(convertRawProp(context, rawProps, "gradientTransform", sourceProps.gradientTransform, {}))
      {}
RNSVGLineProps::RNSVGLineProps(
    const PropsParserContext &context,
    const RNSVGLineProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    name(convertRawProp(context, rawProps, "name", sourceProps.name, {})),
    opacity(convertRawProp(context, rawProps, "opacity", sourceProps.opacity, {1.0})),
    matrix(convertRawProp(context, rawProps, "matrix", sourceProps.matrix, {})),
    mask(convertRawProp(context, rawProps, "mask", sourceProps.mask, {})),
    markerStart(convertRawProp(context, rawProps, "markerStart", sourceProps.markerStart, {})),
    markerMid(convertRawProp(context, rawProps, "markerMid", sourceProps.markerMid, {})),
    markerEnd(convertRawProp(context, rawProps, "markerEnd", sourceProps.markerEnd, {})),
    clipPath(convertRawProp(context, rawProps, "clipPath", sourceProps.clipPath, {})),
    clipRule(convertRawProp(context, rawProps, "clipRule", sourceProps.clipRule, {0})),
    responsible(convertRawProp(context, rawProps, "responsible", sourceProps.responsible, {false})),
    display(convertRawProp(context, rawProps, "display", sourceProps.display, {})),
    pointerEvents(convertRawProp(context, rawProps, "pointerEvents", sourceProps.pointerEvents, {})),
    color(convertRawProp(context, rawProps, "color", sourceProps.color, {})),
    fill(convertRawProp(context, rawProps, "fill", sourceProps.fill, {})),
    fillOpacity(convertRawProp(context, rawProps, "fillOpacity", sourceProps.fillOpacity, {1.0})),
    fillRule(convertRawProp(context, rawProps, "fillRule", sourceProps.fillRule, {1})),
    stroke(convertRawProp(context, rawProps, "stroke", sourceProps.stroke, {})),
    strokeOpacity(convertRawProp(context, rawProps, "strokeOpacity", sourceProps.strokeOpacity, {1.0})),
    strokeWidth(convertRawProp(context, rawProps, "strokeWidth", sourceProps.strokeWidth, {})),
    strokeLinecap(convertRawProp(context, rawProps, "strokeLinecap", sourceProps.strokeLinecap, {0})),
    strokeLinejoin(convertRawProp(context, rawProps, "strokeLinejoin", sourceProps.strokeLinejoin, {0})),
    strokeDasharray(convertRawProp(context, rawProps, "strokeDasharray", sourceProps.strokeDasharray, {})),
    strokeDashoffset(convertRawProp(context, rawProps, "strokeDashoffset", sourceProps.strokeDashoffset, {0.0})),
    strokeMiterlimit(convertRawProp(context, rawProps, "strokeMiterlimit", sourceProps.strokeMiterlimit, {0.0})),
    vectorEffect(convertRawProp(context, rawProps, "vectorEffect", sourceProps.vectorEffect, {0})),
    propList(convertRawProp(context, rawProps, "propList", sourceProps.propList, {})),
    filter(convertRawProp(context, rawProps, "filter", sourceProps.filter, {})),
    x1(convertRawProp(context, rawProps, "x1", sourceProps.x1, {})),
    y1(convertRawProp(context, rawProps, "y1", sourceProps.y1, {})),
    x2(convertRawProp(context, rawProps, "x2", sourceProps.x2, {})),
    y2(convertRawProp(context, rawProps, "y2", sourceProps.y2, {}))
      {}
RNSVGMarkerProps::RNSVGMarkerProps(
    const PropsParserContext &context,
    const RNSVGMarkerProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    name(convertRawProp(context, rawProps, "name", sourceProps.name, {})),
    opacity(convertRawProp(context, rawProps, "opacity", sourceProps.opacity, {1.0})),
    matrix(convertRawProp(context, rawProps, "matrix", sourceProps.matrix, {})),
    mask(convertRawProp(context, rawProps, "mask", sourceProps.mask, {})),
    markerStart(convertRawProp(context, rawProps, "markerStart", sourceProps.markerStart, {})),
    markerMid(convertRawProp(context, rawProps, "markerMid", sourceProps.markerMid, {})),
    markerEnd(convertRawProp(context, rawProps, "markerEnd", sourceProps.markerEnd, {})),
    clipPath(convertRawProp(context, rawProps, "clipPath", sourceProps.clipPath, {})),
    clipRule(convertRawProp(context, rawProps, "clipRule", sourceProps.clipRule, {0})),
    responsible(convertRawProp(context, rawProps, "responsible", sourceProps.responsible, {false})),
    display(convertRawProp(context, rawProps, "display", sourceProps.display, {})),
    pointerEvents(convertRawProp(context, rawProps, "pointerEvents", sourceProps.pointerEvents, {})),
    color(convertRawProp(context, rawProps, "color", sourceProps.color, {})),
    fill(convertRawProp(context, rawProps, "fill", sourceProps.fill, {})),
    fillOpacity(convertRawProp(context, rawProps, "fillOpacity", sourceProps.fillOpacity, {1.0})),
    fillRule(convertRawProp(context, rawProps, "fillRule", sourceProps.fillRule, {1})),
    stroke(convertRawProp(context, rawProps, "stroke", sourceProps.stroke, {})),
    strokeOpacity(convertRawProp(context, rawProps, "strokeOpacity", sourceProps.strokeOpacity, {1.0})),
    strokeWidth(convertRawProp(context, rawProps, "strokeWidth", sourceProps.strokeWidth, {})),
    strokeLinecap(convertRawProp(context, rawProps, "strokeLinecap", sourceProps.strokeLinecap, {0})),
    strokeLinejoin(convertRawProp(context, rawProps, "strokeLinejoin", sourceProps.strokeLinejoin, {0})),
    strokeDasharray(convertRawProp(context, rawProps, "strokeDasharray", sourceProps.strokeDasharray, {})),
    strokeDashoffset(convertRawProp(context, rawProps, "strokeDashoffset", sourceProps.strokeDashoffset, {0.0})),
    strokeMiterlimit(convertRawProp(context, rawProps, "strokeMiterlimit", sourceProps.strokeMiterlimit, {0.0})),
    vectorEffect(convertRawProp(context, rawProps, "vectorEffect", sourceProps.vectorEffect, {0})),
    propList(convertRawProp(context, rawProps, "propList", sourceProps.propList, {})),
    filter(convertRawProp(context, rawProps, "filter", sourceProps.filter, {})),
    fontSize(convertRawProp(context, rawProps, "fontSize", sourceProps.fontSize, {})),
    fontWeight(convertRawProp(context, rawProps, "fontWeight", sourceProps.fontWeight, {})),
    font(convertRawProp(context, rawProps, "font", sourceProps.font, {})),
    refX(convertRawProp(context, rawProps, "refX", sourceProps.refX, {})),
    refY(convertRawProp(context, rawProps, "refY", sourceProps.refY, {})),
    markerHeight(convertRawProp(context, rawProps, "markerHeight", sourceProps.markerHeight, {})),
    markerWidth(convertRawProp(context, rawProps, "markerWidth", sourceProps.markerWidth, {})),
    markerUnits(convertRawProp(context, rawProps, "markerUnits", sourceProps.markerUnits, {})),
    orient(convertRawProp(context, rawProps, "orient", sourceProps.orient, {})),
    minX(convertRawProp(context, rawProps, "minX", sourceProps.minX, {0.0})),
    minY(convertRawProp(context, rawProps, "minY", sourceProps.minY, {0.0})),
    vbWidth(convertRawProp(context, rawProps, "vbWidth", sourceProps.vbWidth, {0.0})),
    vbHeight(convertRawProp(context, rawProps, "vbHeight", sourceProps.vbHeight, {0.0})),
    align(convertRawProp(context, rawProps, "align", sourceProps.align, {})),
    meetOrSlice(convertRawProp(context, rawProps, "meetOrSlice", sourceProps.meetOrSlice, {0}))
      {}
RNSVGMaskProps::RNSVGMaskProps(
    const PropsParserContext &context,
    const RNSVGMaskProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    name(convertRawProp(context, rawProps, "name", sourceProps.name, {})),
    opacity(convertRawProp(context, rawProps, "opacity", sourceProps.opacity, {1.0})),
    matrix(convertRawProp(context, rawProps, "matrix", sourceProps.matrix, {})),
    mask(convertRawProp(context, rawProps, "mask", sourceProps.mask, {})),
    markerStart(convertRawProp(context, rawProps, "markerStart", sourceProps.markerStart, {})),
    markerMid(convertRawProp(context, rawProps, "markerMid", sourceProps.markerMid, {})),
    markerEnd(convertRawProp(context, rawProps, "markerEnd", sourceProps.markerEnd, {})),
    clipPath(convertRawProp(context, rawProps, "clipPath", sourceProps.clipPath, {})),
    clipRule(convertRawProp(context, rawProps, "clipRule", sourceProps.clipRule, {0})),
    responsible(convertRawProp(context, rawProps, "responsible", sourceProps.responsible, {false})),
    display(convertRawProp(context, rawProps, "display", sourceProps.display, {})),
    pointerEvents(convertRawProp(context, rawProps, "pointerEvents", sourceProps.pointerEvents, {})),
    color(convertRawProp(context, rawProps, "color", sourceProps.color, {})),
    fill(convertRawProp(context, rawProps, "fill", sourceProps.fill, {})),
    fillOpacity(convertRawProp(context, rawProps, "fillOpacity", sourceProps.fillOpacity, {1.0})),
    fillRule(convertRawProp(context, rawProps, "fillRule", sourceProps.fillRule, {1})),
    stroke(convertRawProp(context, rawProps, "stroke", sourceProps.stroke, {})),
    strokeOpacity(convertRawProp(context, rawProps, "strokeOpacity", sourceProps.strokeOpacity, {1.0})),
    strokeWidth(convertRawProp(context, rawProps, "strokeWidth", sourceProps.strokeWidth, {})),
    strokeLinecap(convertRawProp(context, rawProps, "strokeLinecap", sourceProps.strokeLinecap, {0})),
    strokeLinejoin(convertRawProp(context, rawProps, "strokeLinejoin", sourceProps.strokeLinejoin, {0})),
    strokeDasharray(convertRawProp(context, rawProps, "strokeDasharray", sourceProps.strokeDasharray, {})),
    strokeDashoffset(convertRawProp(context, rawProps, "strokeDashoffset", sourceProps.strokeDashoffset, {0.0})),
    strokeMiterlimit(convertRawProp(context, rawProps, "strokeMiterlimit", sourceProps.strokeMiterlimit, {0.0})),
    vectorEffect(convertRawProp(context, rawProps, "vectorEffect", sourceProps.vectorEffect, {0})),
    propList(convertRawProp(context, rawProps, "propList", sourceProps.propList, {})),
    filter(convertRawProp(context, rawProps, "filter", sourceProps.filter, {})),
    fontSize(convertRawProp(context, rawProps, "fontSize", sourceProps.fontSize, {})),
    fontWeight(convertRawProp(context, rawProps, "fontWeight", sourceProps.fontWeight, {})),
    font(convertRawProp(context, rawProps, "font", sourceProps.font, {})),
    x(convertRawProp(context, rawProps, "x", sourceProps.x, {})),
    y(convertRawProp(context, rawProps, "y", sourceProps.y, {})),
    height(convertRawProp(context, rawProps, "height", sourceProps.height, {})),
    width(convertRawProp(context, rawProps, "width", sourceProps.width, {})),
    maskUnits(convertRawProp(context, rawProps, "maskUnits", sourceProps.maskUnits, {0})),
    maskContentUnits(convertRawProp(context, rawProps, "maskContentUnits", sourceProps.maskContentUnits, {0})),
    maskType(convertRawProp(context, rawProps, "maskType", sourceProps.maskType, {0}))
      {}
RNSVGPathProps::RNSVGPathProps(
    const PropsParserContext &context,
    const RNSVGPathProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    name(convertRawProp(context, rawProps, "name", sourceProps.name, {})),
    opacity(convertRawProp(context, rawProps, "opacity", sourceProps.opacity, {1.0})),
    matrix(convertRawProp(context, rawProps, "matrix", sourceProps.matrix, {})),
    mask(convertRawProp(context, rawProps, "mask", sourceProps.mask, {})),
    markerStart(convertRawProp(context, rawProps, "markerStart", sourceProps.markerStart, {})),
    markerMid(convertRawProp(context, rawProps, "markerMid", sourceProps.markerMid, {})),
    markerEnd(convertRawProp(context, rawProps, "markerEnd", sourceProps.markerEnd, {})),
    clipPath(convertRawProp(context, rawProps, "clipPath", sourceProps.clipPath, {})),
    clipRule(convertRawProp(context, rawProps, "clipRule", sourceProps.clipRule, {0})),
    responsible(convertRawProp(context, rawProps, "responsible", sourceProps.responsible, {false})),
    display(convertRawProp(context, rawProps, "display", sourceProps.display, {})),
    pointerEvents(convertRawProp(context, rawProps, "pointerEvents", sourceProps.pointerEvents, {})),
    color(convertRawProp(context, rawProps, "color", sourceProps.color, {})),
    fill(convertRawProp(context, rawProps, "fill", sourceProps.fill, {})),
    fillOpacity(convertRawProp(context, rawProps, "fillOpacity", sourceProps.fillOpacity, {1.0})),
    fillRule(convertRawProp(context, rawProps, "fillRule", sourceProps.fillRule, {1})),
    stroke(convertRawProp(context, rawProps, "stroke", sourceProps.stroke, {})),
    strokeOpacity(convertRawProp(context, rawProps, "strokeOpacity", sourceProps.strokeOpacity, {1.0})),
    strokeWidth(convertRawProp(context, rawProps, "strokeWidth", sourceProps.strokeWidth, {})),
    strokeLinecap(convertRawProp(context, rawProps, "strokeLinecap", sourceProps.strokeLinecap, {0})),
    strokeLinejoin(convertRawProp(context, rawProps, "strokeLinejoin", sourceProps.strokeLinejoin, {0})),
    strokeDasharray(convertRawProp(context, rawProps, "strokeDasharray", sourceProps.strokeDasharray, {})),
    strokeDashoffset(convertRawProp(context, rawProps, "strokeDashoffset", sourceProps.strokeDashoffset, {0.0})),
    strokeMiterlimit(convertRawProp(context, rawProps, "strokeMiterlimit", sourceProps.strokeMiterlimit, {0.0})),
    vectorEffect(convertRawProp(context, rawProps, "vectorEffect", sourceProps.vectorEffect, {0})),
    propList(convertRawProp(context, rawProps, "propList", sourceProps.propList, {})),
    filter(convertRawProp(context, rawProps, "filter", sourceProps.filter, {})),
    d(convertRawProp(context, rawProps, "d", sourceProps.d, {}))
      {}
RNSVGPatternProps::RNSVGPatternProps(
    const PropsParserContext &context,
    const RNSVGPatternProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    name(convertRawProp(context, rawProps, "name", sourceProps.name, {})),
    opacity(convertRawProp(context, rawProps, "opacity", sourceProps.opacity, {1.0})),
    matrix(convertRawProp(context, rawProps, "matrix", sourceProps.matrix, {})),
    mask(convertRawProp(context, rawProps, "mask", sourceProps.mask, {})),
    markerStart(convertRawProp(context, rawProps, "markerStart", sourceProps.markerStart, {})),
    markerMid(convertRawProp(context, rawProps, "markerMid", sourceProps.markerMid, {})),
    markerEnd(convertRawProp(context, rawProps, "markerEnd", sourceProps.markerEnd, {})),
    clipPath(convertRawProp(context, rawProps, "clipPath", sourceProps.clipPath, {})),
    clipRule(convertRawProp(context, rawProps, "clipRule", sourceProps.clipRule, {0})),
    responsible(convertRawProp(context, rawProps, "responsible", sourceProps.responsible, {false})),
    display(convertRawProp(context, rawProps, "display", sourceProps.display, {})),
    pointerEvents(convertRawProp(context, rawProps, "pointerEvents", sourceProps.pointerEvents, {})),
    color(convertRawProp(context, rawProps, "color", sourceProps.color, {})),
    fill(convertRawProp(context, rawProps, "fill", sourceProps.fill, {})),
    fillOpacity(convertRawProp(context, rawProps, "fillOpacity", sourceProps.fillOpacity, {1.0})),
    fillRule(convertRawProp(context, rawProps, "fillRule", sourceProps.fillRule, {1})),
    stroke(convertRawProp(context, rawProps, "stroke", sourceProps.stroke, {})),
    strokeOpacity(convertRawProp(context, rawProps, "strokeOpacity", sourceProps.strokeOpacity, {1.0})),
    strokeWidth(convertRawProp(context, rawProps, "strokeWidth", sourceProps.strokeWidth, {})),
    strokeLinecap(convertRawProp(context, rawProps, "strokeLinecap", sourceProps.strokeLinecap, {0})),
    strokeLinejoin(convertRawProp(context, rawProps, "strokeLinejoin", sourceProps.strokeLinejoin, {0})),
    strokeDasharray(convertRawProp(context, rawProps, "strokeDasharray", sourceProps.strokeDasharray, {})),
    strokeDashoffset(convertRawProp(context, rawProps, "strokeDashoffset", sourceProps.strokeDashoffset, {0.0})),
    strokeMiterlimit(convertRawProp(context, rawProps, "strokeMiterlimit", sourceProps.strokeMiterlimit, {0.0})),
    vectorEffect(convertRawProp(context, rawProps, "vectorEffect", sourceProps.vectorEffect, {0})),
    propList(convertRawProp(context, rawProps, "propList", sourceProps.propList, {})),
    filter(convertRawProp(context, rawProps, "filter", sourceProps.filter, {})),
    fontSize(convertRawProp(context, rawProps, "fontSize", sourceProps.fontSize, {})),
    fontWeight(convertRawProp(context, rawProps, "fontWeight", sourceProps.fontWeight, {})),
    font(convertRawProp(context, rawProps, "font", sourceProps.font, {})),
    x(convertRawProp(context, rawProps, "x", sourceProps.x, {})),
    y(convertRawProp(context, rawProps, "y", sourceProps.y, {})),
    height(convertRawProp(context, rawProps, "height", sourceProps.height, {})),
    width(convertRawProp(context, rawProps, "width", sourceProps.width, {})),
    patternUnits(convertRawProp(context, rawProps, "patternUnits", sourceProps.patternUnits, {0})),
    patternContentUnits(convertRawProp(context, rawProps, "patternContentUnits", sourceProps.patternContentUnits, {0})),
    patternTransform(convertRawProp(context, rawProps, "patternTransform", sourceProps.patternTransform, {})),
    minX(convertRawProp(context, rawProps, "minX", sourceProps.minX, {0.0})),
    minY(convertRawProp(context, rawProps, "minY", sourceProps.minY, {0.0})),
    vbWidth(convertRawProp(context, rawProps, "vbWidth", sourceProps.vbWidth, {0.0})),
    vbHeight(convertRawProp(context, rawProps, "vbHeight", sourceProps.vbHeight, {0.0})),
    align(convertRawProp(context, rawProps, "align", sourceProps.align, {})),
    meetOrSlice(convertRawProp(context, rawProps, "meetOrSlice", sourceProps.meetOrSlice, {0}))
      {}
RNSVGRadialGradientProps::RNSVGRadialGradientProps(
    const PropsParserContext &context,
    const RNSVGRadialGradientProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    name(convertRawProp(context, rawProps, "name", sourceProps.name, {})),
    opacity(convertRawProp(context, rawProps, "opacity", sourceProps.opacity, {1.0})),
    matrix(convertRawProp(context, rawProps, "matrix", sourceProps.matrix, {})),
    mask(convertRawProp(context, rawProps, "mask", sourceProps.mask, {})),
    markerStart(convertRawProp(context, rawProps, "markerStart", sourceProps.markerStart, {})),
    markerMid(convertRawProp(context, rawProps, "markerMid", sourceProps.markerMid, {})),
    markerEnd(convertRawProp(context, rawProps, "markerEnd", sourceProps.markerEnd, {})),
    clipPath(convertRawProp(context, rawProps, "clipPath", sourceProps.clipPath, {})),
    clipRule(convertRawProp(context, rawProps, "clipRule", sourceProps.clipRule, {0})),
    responsible(convertRawProp(context, rawProps, "responsible", sourceProps.responsible, {false})),
    display(convertRawProp(context, rawProps, "display", sourceProps.display, {})),
    pointerEvents(convertRawProp(context, rawProps, "pointerEvents", sourceProps.pointerEvents, {})),
    fx(convertRawProp(context, rawProps, "fx", sourceProps.fx, {})),
    fy(convertRawProp(context, rawProps, "fy", sourceProps.fy, {})),
    cx(convertRawProp(context, rawProps, "cx", sourceProps.cx, {})),
    cy(convertRawProp(context, rawProps, "cy", sourceProps.cy, {})),
    rx(convertRawProp(context, rawProps, "rx", sourceProps.rx, {})),
    ry(convertRawProp(context, rawProps, "ry", sourceProps.ry, {})),
    gradient(convertRawProp(context, rawProps, "gradient", sourceProps.gradient, {})),
    gradientUnits(convertRawProp(context, rawProps, "gradientUnits", sourceProps.gradientUnits, {0})),
    gradientTransform(convertRawProp(context, rawProps, "gradientTransform", sourceProps.gradientTransform, {}))
      {}
RNSVGRectProps::RNSVGRectProps(
    const PropsParserContext &context,
    const RNSVGRectProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    name(convertRawProp(context, rawProps, "name", sourceProps.name, {})),
    opacity(convertRawProp(context, rawProps, "opacity", sourceProps.opacity, {1.0})),
    matrix(convertRawProp(context, rawProps, "matrix", sourceProps.matrix, {})),
    mask(convertRawProp(context, rawProps, "mask", sourceProps.mask, {})),
    markerStart(convertRawProp(context, rawProps, "markerStart", sourceProps.markerStart, {})),
    markerMid(convertRawProp(context, rawProps, "markerMid", sourceProps.markerMid, {})),
    markerEnd(convertRawProp(context, rawProps, "markerEnd", sourceProps.markerEnd, {})),
    clipPath(convertRawProp(context, rawProps, "clipPath", sourceProps.clipPath, {})),
    clipRule(convertRawProp(context, rawProps, "clipRule", sourceProps.clipRule, {0})),
    responsible(convertRawProp(context, rawProps, "responsible", sourceProps.responsible, {false})),
    display(convertRawProp(context, rawProps, "display", sourceProps.display, {})),
    pointerEvents(convertRawProp(context, rawProps, "pointerEvents", sourceProps.pointerEvents, {})),
    color(convertRawProp(context, rawProps, "color", sourceProps.color, {})),
    fill(convertRawProp(context, rawProps, "fill", sourceProps.fill, {})),
    fillOpacity(convertRawProp(context, rawProps, "fillOpacity", sourceProps.fillOpacity, {1.0})),
    fillRule(convertRawProp(context, rawProps, "fillRule", sourceProps.fillRule, {1})),
    stroke(convertRawProp(context, rawProps, "stroke", sourceProps.stroke, {})),
    strokeOpacity(convertRawProp(context, rawProps, "strokeOpacity", sourceProps.strokeOpacity, {1.0})),
    strokeWidth(convertRawProp(context, rawProps, "strokeWidth", sourceProps.strokeWidth, {})),
    strokeLinecap(convertRawProp(context, rawProps, "strokeLinecap", sourceProps.strokeLinecap, {0})),
    strokeLinejoin(convertRawProp(context, rawProps, "strokeLinejoin", sourceProps.strokeLinejoin, {0})),
    strokeDasharray(convertRawProp(context, rawProps, "strokeDasharray", sourceProps.strokeDasharray, {})),
    strokeDashoffset(convertRawProp(context, rawProps, "strokeDashoffset", sourceProps.strokeDashoffset, {0.0})),
    strokeMiterlimit(convertRawProp(context, rawProps, "strokeMiterlimit", sourceProps.strokeMiterlimit, {0.0})),
    vectorEffect(convertRawProp(context, rawProps, "vectorEffect", sourceProps.vectorEffect, {0})),
    propList(convertRawProp(context, rawProps, "propList", sourceProps.propList, {})),
    filter(convertRawProp(context, rawProps, "filter", sourceProps.filter, {})),
    x(convertRawProp(context, rawProps, "x", sourceProps.x, {})),
    y(convertRawProp(context, rawProps, "y", sourceProps.y, {})),
    height(convertRawProp(context, rawProps, "height", sourceProps.height, {})),
    width(convertRawProp(context, rawProps, "width", sourceProps.width, {})),
    rx(convertRawProp(context, rawProps, "rx", sourceProps.rx, {})),
    ry(convertRawProp(context, rawProps, "ry", sourceProps.ry, {}))
      {}
RNSVGSymbolProps::RNSVGSymbolProps(
    const PropsParserContext &context,
    const RNSVGSymbolProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    name(convertRawProp(context, rawProps, "name", sourceProps.name, {})),
    opacity(convertRawProp(context, rawProps, "opacity", sourceProps.opacity, {1.0})),
    matrix(convertRawProp(context, rawProps, "matrix", sourceProps.matrix, {})),
    mask(convertRawProp(context, rawProps, "mask", sourceProps.mask, {})),
    markerStart(convertRawProp(context, rawProps, "markerStart", sourceProps.markerStart, {})),
    markerMid(convertRawProp(context, rawProps, "markerMid", sourceProps.markerMid, {})),
    markerEnd(convertRawProp(context, rawProps, "markerEnd", sourceProps.markerEnd, {})),
    clipPath(convertRawProp(context, rawProps, "clipPath", sourceProps.clipPath, {})),
    clipRule(convertRawProp(context, rawProps, "clipRule", sourceProps.clipRule, {0})),
    responsible(convertRawProp(context, rawProps, "responsible", sourceProps.responsible, {false})),
    display(convertRawProp(context, rawProps, "display", sourceProps.display, {})),
    pointerEvents(convertRawProp(context, rawProps, "pointerEvents", sourceProps.pointerEvents, {})),
    color(convertRawProp(context, rawProps, "color", sourceProps.color, {})),
    fill(convertRawProp(context, rawProps, "fill", sourceProps.fill, {})),
    fillOpacity(convertRawProp(context, rawProps, "fillOpacity", sourceProps.fillOpacity, {1.0})),
    fillRule(convertRawProp(context, rawProps, "fillRule", sourceProps.fillRule, {1})),
    stroke(convertRawProp(context, rawProps, "stroke", sourceProps.stroke, {})),
    strokeOpacity(convertRawProp(context, rawProps, "strokeOpacity", sourceProps.strokeOpacity, {1.0})),
    strokeWidth(convertRawProp(context, rawProps, "strokeWidth", sourceProps.strokeWidth, {})),
    strokeLinecap(convertRawProp(context, rawProps, "strokeLinecap", sourceProps.strokeLinecap, {0})),
    strokeLinejoin(convertRawProp(context, rawProps, "strokeLinejoin", sourceProps.strokeLinejoin, {0})),
    strokeDasharray(convertRawProp(context, rawProps, "strokeDasharray", sourceProps.strokeDasharray, {})),
    strokeDashoffset(convertRawProp(context, rawProps, "strokeDashoffset", sourceProps.strokeDashoffset, {0.0})),
    strokeMiterlimit(convertRawProp(context, rawProps, "strokeMiterlimit", sourceProps.strokeMiterlimit, {0.0})),
    vectorEffect(convertRawProp(context, rawProps, "vectorEffect", sourceProps.vectorEffect, {0})),
    propList(convertRawProp(context, rawProps, "propList", sourceProps.propList, {})),
    filter(convertRawProp(context, rawProps, "filter", sourceProps.filter, {})),
    fontSize(convertRawProp(context, rawProps, "fontSize", sourceProps.fontSize, {})),
    fontWeight(convertRawProp(context, rawProps, "fontWeight", sourceProps.fontWeight, {})),
    font(convertRawProp(context, rawProps, "font", sourceProps.font, {})),
    minX(convertRawProp(context, rawProps, "minX", sourceProps.minX, {0.0})),
    minY(convertRawProp(context, rawProps, "minY", sourceProps.minY, {0.0})),
    vbWidth(convertRawProp(context, rawProps, "vbWidth", sourceProps.vbWidth, {0.0})),
    vbHeight(convertRawProp(context, rawProps, "vbHeight", sourceProps.vbHeight, {0.0})),
    align(convertRawProp(context, rawProps, "align", sourceProps.align, {})),
    meetOrSlice(convertRawProp(context, rawProps, "meetOrSlice", sourceProps.meetOrSlice, {0}))
      {}
RNSVGTextProps::RNSVGTextProps(
    const PropsParserContext &context,
    const RNSVGTextProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    name(convertRawProp(context, rawProps, "name", sourceProps.name, {})),
    opacity(convertRawProp(context, rawProps, "opacity", sourceProps.opacity, {1.0})),
    matrix(convertRawProp(context, rawProps, "matrix", sourceProps.matrix, {})),
    mask(convertRawProp(context, rawProps, "mask", sourceProps.mask, {})),
    markerStart(convertRawProp(context, rawProps, "markerStart", sourceProps.markerStart, {})),
    markerMid(convertRawProp(context, rawProps, "markerMid", sourceProps.markerMid, {})),
    markerEnd(convertRawProp(context, rawProps, "markerEnd", sourceProps.markerEnd, {})),
    clipPath(convertRawProp(context, rawProps, "clipPath", sourceProps.clipPath, {})),
    clipRule(convertRawProp(context, rawProps, "clipRule", sourceProps.clipRule, {0})),
    responsible(convertRawProp(context, rawProps, "responsible", sourceProps.responsible, {false})),
    display(convertRawProp(context, rawProps, "display", sourceProps.display, {})),
    pointerEvents(convertRawProp(context, rawProps, "pointerEvents", sourceProps.pointerEvents, {})),
    color(convertRawProp(context, rawProps, "color", sourceProps.color, {})),
    fill(convertRawProp(context, rawProps, "fill", sourceProps.fill, {})),
    fillOpacity(convertRawProp(context, rawProps, "fillOpacity", sourceProps.fillOpacity, {1.0})),
    fillRule(convertRawProp(context, rawProps, "fillRule", sourceProps.fillRule, {1})),
    stroke(convertRawProp(context, rawProps, "stroke", sourceProps.stroke, {})),
    strokeOpacity(convertRawProp(context, rawProps, "strokeOpacity", sourceProps.strokeOpacity, {1.0})),
    strokeWidth(convertRawProp(context, rawProps, "strokeWidth", sourceProps.strokeWidth, {})),
    strokeLinecap(convertRawProp(context, rawProps, "strokeLinecap", sourceProps.strokeLinecap, {0})),
    strokeLinejoin(convertRawProp(context, rawProps, "strokeLinejoin", sourceProps.strokeLinejoin, {0})),
    strokeDasharray(convertRawProp(context, rawProps, "strokeDasharray", sourceProps.strokeDasharray, {})),
    strokeDashoffset(convertRawProp(context, rawProps, "strokeDashoffset", sourceProps.strokeDashoffset, {0.0})),
    strokeMiterlimit(convertRawProp(context, rawProps, "strokeMiterlimit", sourceProps.strokeMiterlimit, {0.0})),
    vectorEffect(convertRawProp(context, rawProps, "vectorEffect", sourceProps.vectorEffect, {0})),
    propList(convertRawProp(context, rawProps, "propList", sourceProps.propList, {})),
    filter(convertRawProp(context, rawProps, "filter", sourceProps.filter, {})),
    fontSize(convertRawProp(context, rawProps, "fontSize", sourceProps.fontSize, {})),
    fontWeight(convertRawProp(context, rawProps, "fontWeight", sourceProps.fontWeight, {})),
    font(convertRawProp(context, rawProps, "font", sourceProps.font, {})),
    dx(convertRawProp(context, rawProps, "dx", sourceProps.dx, {})),
    dy(convertRawProp(context, rawProps, "dy", sourceProps.dy, {})),
    x(convertRawProp(context, rawProps, "x", sourceProps.x, {})),
    y(convertRawProp(context, rawProps, "y", sourceProps.y, {})),
    rotate(convertRawProp(context, rawProps, "rotate", sourceProps.rotate, {})),
    inlineSize(convertRawProp(context, rawProps, "inlineSize", sourceProps.inlineSize, {})),
    textLength(convertRawProp(context, rawProps, "textLength", sourceProps.textLength, {})),
    baselineShift(convertRawProp(context, rawProps, "baselineShift", sourceProps.baselineShift, {})),
    lengthAdjust(convertRawProp(context, rawProps, "lengthAdjust", sourceProps.lengthAdjust, {})),
    alignmentBaseline(convertRawProp(context, rawProps, "alignmentBaseline", sourceProps.alignmentBaseline, {})),
    verticalAlign(convertRawProp(context, rawProps, "verticalAlign", sourceProps.verticalAlign, {}))
      {}
RNSVGTextPathProps::RNSVGTextPathProps(
    const PropsParserContext &context,
    const RNSVGTextPathProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    name(convertRawProp(context, rawProps, "name", sourceProps.name, {})),
    opacity(convertRawProp(context, rawProps, "opacity", sourceProps.opacity, {1.0})),
    matrix(convertRawProp(context, rawProps, "matrix", sourceProps.matrix, {})),
    mask(convertRawProp(context, rawProps, "mask", sourceProps.mask, {})),
    markerStart(convertRawProp(context, rawProps, "markerStart", sourceProps.markerStart, {})),
    markerMid(convertRawProp(context, rawProps, "markerMid", sourceProps.markerMid, {})),
    markerEnd(convertRawProp(context, rawProps, "markerEnd", sourceProps.markerEnd, {})),
    clipPath(convertRawProp(context, rawProps, "clipPath", sourceProps.clipPath, {})),
    clipRule(convertRawProp(context, rawProps, "clipRule", sourceProps.clipRule, {0})),
    responsible(convertRawProp(context, rawProps, "responsible", sourceProps.responsible, {false})),
    display(convertRawProp(context, rawProps, "display", sourceProps.display, {})),
    pointerEvents(convertRawProp(context, rawProps, "pointerEvents", sourceProps.pointerEvents, {})),
    color(convertRawProp(context, rawProps, "color", sourceProps.color, {})),
    fill(convertRawProp(context, rawProps, "fill", sourceProps.fill, {})),
    fillOpacity(convertRawProp(context, rawProps, "fillOpacity", sourceProps.fillOpacity, {1.0})),
    fillRule(convertRawProp(context, rawProps, "fillRule", sourceProps.fillRule, {1})),
    stroke(convertRawProp(context, rawProps, "stroke", sourceProps.stroke, {})),
    strokeOpacity(convertRawProp(context, rawProps, "strokeOpacity", sourceProps.strokeOpacity, {1.0})),
    strokeWidth(convertRawProp(context, rawProps, "strokeWidth", sourceProps.strokeWidth, {})),
    strokeLinecap(convertRawProp(context, rawProps, "strokeLinecap", sourceProps.strokeLinecap, {0})),
    strokeLinejoin(convertRawProp(context, rawProps, "strokeLinejoin", sourceProps.strokeLinejoin, {0})),
    strokeDasharray(convertRawProp(context, rawProps, "strokeDasharray", sourceProps.strokeDasharray, {})),
    strokeDashoffset(convertRawProp(context, rawProps, "strokeDashoffset", sourceProps.strokeDashoffset, {0.0})),
    strokeMiterlimit(convertRawProp(context, rawProps, "strokeMiterlimit", sourceProps.strokeMiterlimit, {0.0})),
    vectorEffect(convertRawProp(context, rawProps, "vectorEffect", sourceProps.vectorEffect, {0})),
    propList(convertRawProp(context, rawProps, "propList", sourceProps.propList, {})),
    filter(convertRawProp(context, rawProps, "filter", sourceProps.filter, {})),
    fontSize(convertRawProp(context, rawProps, "fontSize", sourceProps.fontSize, {})),
    fontWeight(convertRawProp(context, rawProps, "fontWeight", sourceProps.fontWeight, {})),
    font(convertRawProp(context, rawProps, "font", sourceProps.font, {})),
    dx(convertRawProp(context, rawProps, "dx", sourceProps.dx, {})),
    dy(convertRawProp(context, rawProps, "dy", sourceProps.dy, {})),
    x(convertRawProp(context, rawProps, "x", sourceProps.x, {})),
    y(convertRawProp(context, rawProps, "y", sourceProps.y, {})),
    rotate(convertRawProp(context, rawProps, "rotate", sourceProps.rotate, {})),
    inlineSize(convertRawProp(context, rawProps, "inlineSize", sourceProps.inlineSize, {})),
    textLength(convertRawProp(context, rawProps, "textLength", sourceProps.textLength, {})),
    baselineShift(convertRawProp(context, rawProps, "baselineShift", sourceProps.baselineShift, {})),
    lengthAdjust(convertRawProp(context, rawProps, "lengthAdjust", sourceProps.lengthAdjust, {})),
    alignmentBaseline(convertRawProp(context, rawProps, "alignmentBaseline", sourceProps.alignmentBaseline, {})),
    verticalAlign(convertRawProp(context, rawProps, "verticalAlign", sourceProps.verticalAlign, {})),
    href(convertRawProp(context, rawProps, "href", sourceProps.href, {})),
    side(convertRawProp(context, rawProps, "side", sourceProps.side, {})),
    method(convertRawProp(context, rawProps, "method", sourceProps.method, {})),
    midLine(convertRawProp(context, rawProps, "midLine", sourceProps.midLine, {})),
    spacing(convertRawProp(context, rawProps, "spacing", sourceProps.spacing, {})),
    startOffset(convertRawProp(context, rawProps, "startOffset", sourceProps.startOffset, {}))
      {}
RNSVGTSpanProps::RNSVGTSpanProps(
    const PropsParserContext &context,
    const RNSVGTSpanProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    name(convertRawProp(context, rawProps, "name", sourceProps.name, {})),
    opacity(convertRawProp(context, rawProps, "opacity", sourceProps.opacity, {1.0})),
    matrix(convertRawProp(context, rawProps, "matrix", sourceProps.matrix, {})),
    mask(convertRawProp(context, rawProps, "mask", sourceProps.mask, {})),
    markerStart(convertRawProp(context, rawProps, "markerStart", sourceProps.markerStart, {})),
    markerMid(convertRawProp(context, rawProps, "markerMid", sourceProps.markerMid, {})),
    markerEnd(convertRawProp(context, rawProps, "markerEnd", sourceProps.markerEnd, {})),
    clipPath(convertRawProp(context, rawProps, "clipPath", sourceProps.clipPath, {})),
    clipRule(convertRawProp(context, rawProps, "clipRule", sourceProps.clipRule, {0})),
    responsible(convertRawProp(context, rawProps, "responsible", sourceProps.responsible, {false})),
    display(convertRawProp(context, rawProps, "display", sourceProps.display, {})),
    pointerEvents(convertRawProp(context, rawProps, "pointerEvents", sourceProps.pointerEvents, {})),
    color(convertRawProp(context, rawProps, "color", sourceProps.color, {})),
    fill(convertRawProp(context, rawProps, "fill", sourceProps.fill, {})),
    fillOpacity(convertRawProp(context, rawProps, "fillOpacity", sourceProps.fillOpacity, {1.0})),
    fillRule(convertRawProp(context, rawProps, "fillRule", sourceProps.fillRule, {1})),
    stroke(convertRawProp(context, rawProps, "stroke", sourceProps.stroke, {})),
    strokeOpacity(convertRawProp(context, rawProps, "strokeOpacity", sourceProps.strokeOpacity, {1.0})),
    strokeWidth(convertRawProp(context, rawProps, "strokeWidth", sourceProps.strokeWidth, {})),
    strokeLinecap(convertRawProp(context, rawProps, "strokeLinecap", sourceProps.strokeLinecap, {0})),
    strokeLinejoin(convertRawProp(context, rawProps, "strokeLinejoin", sourceProps.strokeLinejoin, {0})),
    strokeDasharray(convertRawProp(context, rawProps, "strokeDasharray", sourceProps.strokeDasharray, {})),
    strokeDashoffset(convertRawProp(context, rawProps, "strokeDashoffset", sourceProps.strokeDashoffset, {0.0})),
    strokeMiterlimit(convertRawProp(context, rawProps, "strokeMiterlimit", sourceProps.strokeMiterlimit, {0.0})),
    vectorEffect(convertRawProp(context, rawProps, "vectorEffect", sourceProps.vectorEffect, {0})),
    propList(convertRawProp(context, rawProps, "propList", sourceProps.propList, {})),
    filter(convertRawProp(context, rawProps, "filter", sourceProps.filter, {})),
    fontSize(convertRawProp(context, rawProps, "fontSize", sourceProps.fontSize, {})),
    fontWeight(convertRawProp(context, rawProps, "fontWeight", sourceProps.fontWeight, {})),
    font(convertRawProp(context, rawProps, "font", sourceProps.font, {})),
    dx(convertRawProp(context, rawProps, "dx", sourceProps.dx, {})),
    dy(convertRawProp(context, rawProps, "dy", sourceProps.dy, {})),
    x(convertRawProp(context, rawProps, "x", sourceProps.x, {})),
    y(convertRawProp(context, rawProps, "y", sourceProps.y, {})),
    rotate(convertRawProp(context, rawProps, "rotate", sourceProps.rotate, {})),
    inlineSize(convertRawProp(context, rawProps, "inlineSize", sourceProps.inlineSize, {})),
    textLength(convertRawProp(context, rawProps, "textLength", sourceProps.textLength, {})),
    baselineShift(convertRawProp(context, rawProps, "baselineShift", sourceProps.baselineShift, {})),
    lengthAdjust(convertRawProp(context, rawProps, "lengthAdjust", sourceProps.lengthAdjust, {})),
    alignmentBaseline(convertRawProp(context, rawProps, "alignmentBaseline", sourceProps.alignmentBaseline, {})),
    verticalAlign(convertRawProp(context, rawProps, "verticalAlign", sourceProps.verticalAlign, {})),
    content(convertRawProp(context, rawProps, "content", sourceProps.content, {}))
      {}
RNSVGUseProps::RNSVGUseProps(
    const PropsParserContext &context,
    const RNSVGUseProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    name(convertRawProp(context, rawProps, "name", sourceProps.name, {})),
    opacity(convertRawProp(context, rawProps, "opacity", sourceProps.opacity, {1.0})),
    matrix(convertRawProp(context, rawProps, "matrix", sourceProps.matrix, {})),
    mask(convertRawProp(context, rawProps, "mask", sourceProps.mask, {})),
    markerStart(convertRawProp(context, rawProps, "markerStart", sourceProps.markerStart, {})),
    markerMid(convertRawProp(context, rawProps, "markerMid", sourceProps.markerMid, {})),
    markerEnd(convertRawProp(context, rawProps, "markerEnd", sourceProps.markerEnd, {})),
    clipPath(convertRawProp(context, rawProps, "clipPath", sourceProps.clipPath, {})),
    clipRule(convertRawProp(context, rawProps, "clipRule", sourceProps.clipRule, {0})),
    responsible(convertRawProp(context, rawProps, "responsible", sourceProps.responsible, {false})),
    display(convertRawProp(context, rawProps, "display", sourceProps.display, {})),
    pointerEvents(convertRawProp(context, rawProps, "pointerEvents", sourceProps.pointerEvents, {})),
    color(convertRawProp(context, rawProps, "color", sourceProps.color, {})),
    fill(convertRawProp(context, rawProps, "fill", sourceProps.fill, {})),
    fillOpacity(convertRawProp(context, rawProps, "fillOpacity", sourceProps.fillOpacity, {1.0})),
    fillRule(convertRawProp(context, rawProps, "fillRule", sourceProps.fillRule, {1})),
    stroke(convertRawProp(context, rawProps, "stroke", sourceProps.stroke, {})),
    strokeOpacity(convertRawProp(context, rawProps, "strokeOpacity", sourceProps.strokeOpacity, {1.0})),
    strokeWidth(convertRawProp(context, rawProps, "strokeWidth", sourceProps.strokeWidth, {})),
    strokeLinecap(convertRawProp(context, rawProps, "strokeLinecap", sourceProps.strokeLinecap, {0})),
    strokeLinejoin(convertRawProp(context, rawProps, "strokeLinejoin", sourceProps.strokeLinejoin, {0})),
    strokeDasharray(convertRawProp(context, rawProps, "strokeDasharray", sourceProps.strokeDasharray, {})),
    strokeDashoffset(convertRawProp(context, rawProps, "strokeDashoffset", sourceProps.strokeDashoffset, {0.0})),
    strokeMiterlimit(convertRawProp(context, rawProps, "strokeMiterlimit", sourceProps.strokeMiterlimit, {0.0})),
    vectorEffect(convertRawProp(context, rawProps, "vectorEffect", sourceProps.vectorEffect, {0})),
    propList(convertRawProp(context, rawProps, "propList", sourceProps.propList, {})),
    filter(convertRawProp(context, rawProps, "filter", sourceProps.filter, {})),
    href(convertRawProp(context, rawProps, "href", sourceProps.href, {})),
    x(convertRawProp(context, rawProps, "x", sourceProps.x, {})),
    y(convertRawProp(context, rawProps, "y", sourceProps.y, {})),
    height(convertRawProp(context, rawProps, "height", sourceProps.height, {})),
    width(convertRawProp(context, rawProps, "width", sourceProps.width, {}))
      {}

} // namespace facebook::react
