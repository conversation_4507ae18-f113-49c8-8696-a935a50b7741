
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GeneratePropsCpp.js
 */

#include <react/renderer/components/safeareacontext/Props.h>
#include <react/renderer/core/PropsParserContext.h>
#include <react/renderer/core/propsConversions.h>

namespace facebook::react {

RNCSafeAreaProviderProps::RNCSafeAreaProviderProps(
    const PropsParserContext &context,
    const RNCSafeAreaProviderProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps)

    
      {}
RNCSafeAreaViewProps::RNCSafeAreaViewProps(
    const PropsParserContext &context,
    const RNCSafeAreaViewProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    mode(convertRawProp(context, rawProps, "mode", sourceProps.mode, {RNCSafeAreaViewMode::Padding})),
    edges(convertRawProp(context, rawProps, "edges", sourceProps.edges, {}))
      {}

} // namespace facebook::react
