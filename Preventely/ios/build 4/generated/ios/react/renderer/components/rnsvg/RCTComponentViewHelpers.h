/**
* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
*
* Do not edit this file as changes may cause incorrect behavior and will be lost
* once the code is regenerated.
*
* @generated by codegen project: GenerateComponentHObjCpp.js
*/

#import <Foundation/Foundation.h>
#import <React/RCTDefines.h>
#import <React/RCTLog.h>

NS_ASSUME_NONNULL_BEGIN

@protocol RCTRNSVGCircleViewProtocol <NSObject>

@end

@protocol RCTRNSVGClipPathViewProtocol <NSObject>

@end

@protocol RCTRNSVGDefsViewProtocol <NSObject>

@end

@protocol RCTRNSVGEllipseViewProtocol <NSObject>

@end

@protocol RCTRNSVGFeBlendViewProtocol <NSObject>

@end

@protocol RCTRNSVGFeColorMatrixViewProtocol <NSObject>

@end

@protocol RCTRNSVGFeCompositeViewProtocol <NSObject>

@end

@protocol RCTRNSVGFeFloodViewProtocol <NSObject>

@end

@protocol RCTRNSVGFeGaussianBlurViewProtocol <NSObject>

@end

@protocol RCTRNSVGFeMergeViewProtocol <NSObject>

@end

@protocol RCTRNSVGFeOffsetViewProtocol <NSObject>

@end

@protocol RCTRNSVGFilterViewProtocol <NSObject>

@end

@protocol RCTRNSVGForeignObjectViewProtocol <NSObject>

@end

@protocol RCTRNSVGGroupViewProtocol <NSObject>

@end

@protocol RCTRNSVGImageViewProtocol <NSObject>

@end

@protocol RCTRNSVGSvgViewViewProtocol <NSObject>

@end

@protocol RCTRNSVGLinearGradientViewProtocol <NSObject>

@end

@protocol RCTRNSVGLineViewProtocol <NSObject>

@end

@protocol RCTRNSVGMarkerViewProtocol <NSObject>

@end

@protocol RCTRNSVGMaskViewProtocol <NSObject>

@end

@protocol RCTRNSVGPathViewProtocol <NSObject>

@end

@protocol RCTRNSVGPatternViewProtocol <NSObject>

@end

@protocol RCTRNSVGRadialGradientViewProtocol <NSObject>

@end

@protocol RCTRNSVGRectViewProtocol <NSObject>

@end

@protocol RCTRNSVGSymbolViewProtocol <NSObject>

@end

@protocol RCTRNSVGTextViewProtocol <NSObject>

@end

@protocol RCTRNSVGTextPathViewProtocol <NSObject>

@end

@protocol RCTRNSVGTSpanViewProtocol <NSObject>

@end

@protocol RCTRNSVGUseViewProtocol <NSObject>

@end

NS_ASSUME_NONNULL_END