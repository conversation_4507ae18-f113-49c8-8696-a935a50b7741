/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleObjCpp
 *
 * We create an umbrella header (and corresponding implementation) here since
 * Cxx compilation in BUCK has a limitation: source-code producing genrule()s
 * must have a single output. More files => more genrule()s => slower builds.
 */

#import "RNCWebViewSpec.h"


@implementation NativeRNCWebViewModuleSpecBase


- (void)setEventEmitterCallback:(EventEmitterCallbackWrapper *)eventEmitterCallbackWrapper
{
  _eventEmitterCallback = std::move(eventEmitterCallbackWrapper->_eventEmitterCallback);
}
@end


namespace facebook::react {
  
    static facebook::jsi::Value __hostFunction_NativeRNCWebViewModuleSpecJSI_isFileUploadSupported(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "isFileUploadSupported", @selector(isFileUploadSupported:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeRNCWebViewModuleSpecJSI_shouldStartLoadWithLockIdentifier(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "shouldStartLoadWithLockIdentifier", @selector(shouldStartLoadWithLockIdentifier:lockIdentifier:), args, count);
    }

  NativeRNCWebViewModuleSpecJSI::NativeRNCWebViewModuleSpecJSI(const ObjCTurboModule::InitParams &params)
    : ObjCTurboModule(params) {
      
        methodMap_["isFileUploadSupported"] = MethodMetadata {0, __hostFunction_NativeRNCWebViewModuleSpecJSI_isFileUploadSupported};
        
        
        methodMap_["shouldStartLoadWithLockIdentifier"] = MethodMetadata {2, __hostFunction_NativeRNCWebViewModuleSpecJSI_shouldStartLoadWithLockIdentifier};
        
  }
} // namespace facebook::react
