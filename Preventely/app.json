{"expo": {"name": "Preventely", "slug": "Preventely", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "preventely", "userInterfaceStyle": "automatic", "newArchEnabled": true, "runtimeVersion": {"policy": "sdkVersion"}, "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.majid.preventely", "infoPlist": {"NSCameraUsageDescription": "This app uses the camera to scan barcodes on food products."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "permissions": ["android.permission.CAMERA"], "package": "com.anonymous.Preventely"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-camera"], "experiments": {"typedRoutes": true}, "extra": {"API_URL": "http://localhost:3001/api", "router": {}, "eas": {"projectId": "575f693b-feb1-4be4-aa4d-f02d898d1416"}}}}