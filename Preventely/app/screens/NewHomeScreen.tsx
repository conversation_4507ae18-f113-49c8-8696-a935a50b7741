import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import React, { useState, useEffect } from 'react';
import {
  SafeAreaView,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ActivityIndicator,
  Alert
} from 'react-native';
import apiService from '../services/api.service';
import GlucoseChart from '../components/GlucoseChart';
import { useAuth } from '../contexts/AuthContext';
import { checkEnvironmentVariables } from '../utils/env-test';
import EnvTest from '../components/EnvTest';

// User profile interface
interface UserProfile {
  name: string;
  riskLevel: string;
  streak: number;
  nextCheckup: string;
  weight: number;
  bmi: number;
  targetWeight: number;
  currentGlucose: number;
  glucoseTrend: string; // 'rising', 'falling', 'stable'
  dailyGIScore: number; // 0-100 scale
  dailyGLScore: number; // 0-100 scale
  glucoseTargetMin: number; // Minimum target glucose level
  glucoseTargetMax: number; // Maximum target glucose level
}

// Define reminder type
type ReminderType = 'medication' | 'measurement' | 'activity' | 'meal' | string;

// Define reminder interface
interface Reminder {
  id: string;
  title: string;
  time: string;
  type: ReminderType;
  completed: boolean;
}

// CGM Reading interface
interface CGMReading {
  time: string;
  value: number;
}



// Get GI/GL score status and color
const getScoreStatus = (score: number) => {
  if (score < 55) return { text: 'Good', color: '#4CAF50' }; // Green
  if (score < 80) return { text: 'Moderate', color: '#FFC107' }; // Yellow
  return { text: 'High', color: '#F44336' }; // Red
};

// Get glucose trend indicator
const getGlucoseTrendIcon = (trend: string) => {
  switch (trend) {
    case 'rising':
      return 'arrow-up';
    case 'falling':
      return 'arrow-down';
    default:
      return 'remove'; // horizontal line for stable
  }
};

const NewHomeScreen = () => {
  const router = useRouter();
  const { user: authUser, isAuthenticated } = useAuth();
  
  // State for data from API
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState<UserProfile | null>(null);
  const [reminders, setReminders] = useState<Reminder[]>([]);
  const [cgmData, setCgmData] = useState<CGMReading[]>([]);
  
  // Mock data for development when API fails
  const mockUserProfile = {
    firstName: 'John',
    lastName: 'Doe',
    riskLevel: 'Moderate',
    streak: 5,
    nextCheckup: '2025-06-15',
    weight: 75,
    bmi: 24.5,
    targetWeight: 70,
    glucoseTargetMin: 70,
    glucoseTargetMax: 140
  };

  const mockGlucoseReadings = [
    { timestamp: new Date(Date.now() - 7 * 3600000).toISOString(), value: 95 },
    { timestamp: new Date(Date.now() - 6 * 3600000).toISOString(), value: 110 },
    { timestamp: new Date(Date.now() - 5 * 3600000).toISOString(), value: 125 },
    { timestamp: new Date(Date.now() - 4 * 3600000).toISOString(), value: 118 },
    { timestamp: new Date(Date.now() - 3 * 3600000).toISOString(), value: 105 },
    { timestamp: new Date(Date.now() - 2 * 3600000).toISOString(), value: 98 },
    { timestamp: new Date(Date.now() - 1 * 3600000).toISOString(), value: 102 },
    { timestamp: new Date().toISOString(), value: 108 }
  ];

  const mockReminders = [
    { id: '1', title: 'Take medication', time: '8:00 AM', type: 'medication', completed: false },
    { id: '2', title: 'Check blood sugar', time: '12:00 PM', type: 'measurement', completed: false },
    { id: '3', title: 'Go for a walk', time: '5:00 PM', type: 'activity', completed: false }
  ];

  // Fetch data from API
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Test environment variables
        const envTest = checkEnvironmentVariables();
        console.log('Environment variables test result:', envTest);
        
        // Use authentication state from AuthContext
        if (!isAuthenticated) {
          console.log('User not authenticated, redirecting to login');
          router.replace('/onboarding/login');
          return;
        }
        
        // Initialize variables outside the try block so they're accessible throughout the function
        let formattedReadings: CGMReading[] = [];
        let todayReminders: Reminder[] = [];
        
        try {
          // Fetch user profile
          console.log('Fetching user profile...');
          const profileResponse = await apiService.getUserProfile();
          console.log('Profile response:', profileResponse);
          
          // Extract profile data from the response
          const profileData = profileResponse.data || {};
          const userProfile = profileData.profile || {};
          
          // Fetch latest glucose reading
          console.log('Fetching latest glucose reading...');
          let latestGlucose;
          try {
            latestGlucose = await apiService.getLatestGlucoseReading();
            console.log('Latest glucose reading:', latestGlucose);
            // Validate the glucose reading
            if (latestGlucose && typeof latestGlucose !== 'object') {
              console.log('Invalid glucose reading format, using default');
              latestGlucose = {
                value: 100,
                timestamp: new Date().toISOString(),
                source: 'default'
              };
            }
          } catch (error) {
            console.log('Error processing latest glucose reading, using default');
            latestGlucose = {
              value: 100,
              timestamp: new Date().toISOString(),
              source: 'default'
            };
          }
          
          // Fetch glucose readings for the last 24 hours
          console.log('Fetching glucose readings...');
          const glucoseReadings = await apiService.getGlucoseReadings({
            limit: 8,
            startDate: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
            endDate: new Date().toISOString()
          });
          console.log('Glucose readings fetched:', glucoseReadings?.length || 0);
          
          // Format glucose readings for display - ensure we handle null or undefined values
          formattedReadings = Array.isArray(glucoseReadings) ? glucoseReadings.map((reading: any) => {
            try {
              return {
                time: reading?.timestamp ? new Date(reading.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : 'N/A',
                value: typeof reading?.value === 'number' ? reading.value : 0
              };
            } catch (err) {
              console.log('Error formatting reading:', reading);
              return {
                time: 'N/A',
                value: 0
              };
            }
          }) : [];
          
          // Fetch daily nutrition log for today
          const today = new Date();
          console.log('Fetching nutrition log...');
          const nutritionLog = await apiService.getDailyNutritionLog(today.toISOString().split('T')[0]);
          
          // Fetch reminders for today
          console.log('Fetching reminders...');
          todayReminders = await apiService.getReminders(today.toISOString().split('T')[0]) || [];
          
          // Fetch medications and allergies
          console.log('Fetching medications and allergies...');
          const medications = await apiService.getMedications();
          const allergies = await apiService.getAllergies();
          
          // Calculate GI/GL scores from nutrition log
          const giScore = nutritionLog?.averageGI || 65; // Default if not available
          const glScore = nutritionLog?.totalGlycemicLoad || 72; // Default if not available
          
          // Determine glucose trend
          let glucoseTrend = 'stable';
          if (formattedReadings.length >= 2) {
            const lastReading = formattedReadings[formattedReadings.length - 1].value;
            const previousReading = formattedReadings[formattedReadings.length - 2].value;
            if (lastReading > previousReading + 5) glucoseTrend = 'rising';
            else if (lastReading < previousReading - 5) glucoseTrend = 'falling';
          }
          
          // Set user profile data
          setUser({
            name: profileData.name?.split(' ')[0] || 'User',
            riskLevel: userProfile.condition || 'Moderate',
            streak: userProfile.streak || 1,
            nextCheckup: userProfile.nextCheckup || 'Not scheduled',
            weight: userProfile.weight || 70,
            bmi: userProfile.bmi || 22,
            targetWeight: userProfile.targetWeight || 65,
            currentGlucose: latestGlucose?.value || 100,
            glucoseTrend,
            dailyGIScore: giScore,
            dailyGLScore: glScore,
            glucoseTargetMin: userProfile.glucoseTargetMin || 70,
            glucoseTargetMax: userProfile.glucoseTargetMax || 140
          });
          
          console.log('User data set successfully');
          
          // Set the CGM data and reminders inside the try block
          setCgmData(formattedReadings);
          setReminders(todayReminders);
        } catch (profileError) {
          console.error('Error in profile data fetching:', profileError);
          // If there's an error, use mock data
          const mockFormattedReadings = mockGlucoseReadings.map(reading => ({
            time: new Date(reading.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
            value: reading.value
          }));
          
          setCgmData(mockFormattedReadings);
          setReminders(mockReminders);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        
        // Use mock data when API calls fail
        console.log('Using mock data due to API error');
        
        // Format mock glucose readings for display
        const formattedReadings = mockGlucoseReadings.map((reading) => ({
          time: new Date(reading.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
          value: reading.value
        }));
        
        // Set mock user profile
        setUser({
          name: mockUserProfile.firstName || 'User',
          riskLevel: mockUserProfile.riskLevel || 'Moderate',
          streak: mockUserProfile.streak || 1,
          nextCheckup: mockUserProfile.nextCheckup || 'Not scheduled',
          weight: mockUserProfile.weight || 70,
          bmi: mockUserProfile.bmi || 22,
          targetWeight: mockUserProfile.targetWeight || 65,
          currentGlucose: mockGlucoseReadings[mockGlucoseReadings.length - 1].value,
          glucoseTrend: 'stable',
          dailyGIScore: 65,
          dailyGLScore: 72,
          glucoseTargetMin: mockUserProfile.glucoseTargetMin,
          glucoseTargetMax: mockUserProfile.glucoseTargetMax
        });
        
        setCgmData(formattedReadings);
        setReminders(mockReminders);
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [isAuthenticated]); // Re-fetch data when authentication state changes
  
  // Get current date
  const today = new Date();
  const options: Intl.DateTimeFormatOptions = { weekday: 'long', day: 'numeric', month: 'long' };
  const formattedDate = today.toLocaleDateString('en-US', options);
  
  const handleToggleReminder = async (id: string) => {
    try {
      // Find the reminder to toggle
      const reminderToUpdate = reminders.find(r => r.id === id);
      if (!reminderToUpdate) return;
      
      // Optimistically update UI
      setReminders(prev => 
        prev.map(reminder => 
          reminder.id === id 
            ? { ...reminder, completed: !reminder.completed }
            : reminder
        )
      );
      
      // Update on server
      await apiService.updateReminder(id, { completed: !reminderToUpdate.completed });
    } catch (error) {
      console.error('Error updating reminder:', error);
      Alert.alert('Error', 'Failed to update reminder. Please try again.');
      
      // Revert the change if API call fails
      setReminders(prev => 
        prev.map(reminder => 
          reminder.id === id 
            ? { ...reminder, completed: !reminder.completed }
            : reminder
        )
      );
    }
  };
  
  const handleViewAllReminders = () => {
    router.push('/reminders' as any);
  };
  
  const navigateToFoodTracking = () => {
    router.push('/food-tracking' as any);
  };
  
  const navigateToReports = () => {
    router.push('/reports' as any);
  };
  
  const navigateToAssistant = () => {
    router.push('/ai-assistant' as any);
  };

  const navigateToDexcomConnect = () => {
    router.push('/screens/dexcom-connect' as any);
  };

  // Get GI/GL status for the user
  const giStatus = getScoreStatus(user?.dailyGIScore || 0);
  const glStatus = getScoreStatus(user?.dailyGLScore || 0);
  
  if (loading || !user) {
    return (
      <SafeAreaView style={[styles.container, styles.loadingContainer]}>
        <ActivityIndicator size="large" color="#6B5CE5" />
        <Text style={styles.loadingText}>Loading your health data...</Text>
      </SafeAreaView>
    );
  }
  
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#F1EFFF" />
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header with Date and Welcome */}
        {/* <View style={styles.header}>
          <TouchableOpacity style={styles.menuButton}>
            <Ionicons name="menu-outline" size={20} color="#333333" />
          </TouchableOpacity>
          <View>
            <Text style={styles.dateText}>{formattedDate}</Text>
            <Text style={styles.subDateText}>Day {user.streak} of your health journey</Text>
          </View>
        </View> */}
        
        {/* Welcome Message */}
        <View style={styles.welcomeContainer}>
          <Text style={styles.welcomeText}>Hi {user.name},</Text>
          <Text style={styles.welcomeSubtext}>You're making great progress! Keep up the good work.</Text>
        </View>
        
        {/* Main Metrics Grid - 2x2 layout */}
        <View style={styles.metricsGrid}>
          {/* Top Left - Current Glucose */}
          <View style={[styles.metricCard, styles.purpleLight]}>
            <Text style={styles.metricTitle}>Current Glucose</Text>
            <View style={styles.metricValueContainer}>
              <Text style={[styles.metricValue, styles.purpleText]}>{user.currentGlucose}</Text>
              <Text style={[styles.metricUnit, styles.purpleText]}>mg/dL</Text>
            </View>
            <View style={styles.trendIconContainer}>
              <Ionicons name={getGlucoseTrendIcon(user.glucoseTrend)} size={24} color="#6B5CE5" />
            </View>
          </View>
          
          {/* Top Right - Weight/BMI */}
          <View style={[styles.metricCard, styles.purpleDark]}>
            <Text style={[styles.metricTitle, styles.whiteText]}>Current Weight</Text>
            <Text style={[styles.metricSubtitle, styles.yellowText]}>BMI: {user.bmi}</Text>
            <View style={styles.metricValueContainer}>
              <Text style={[styles.metricValue, styles.whiteText]}>{user.weight}</Text>
              <Text style={[styles.metricUnit, styles.whiteText]}>kg</Text>
            </View>
            <View style={styles.weightIconContainer}>
              <Ionicons name="fitness" size={24} color="#FFCC33" />
            </View>
          </View>
          
          {/* Bottom Left - GI Score */}
          <View style={[styles.metricCard, styles.purpleMedium]}>
            <Text style={[styles.metricTitle, styles.whiteText]}>Glycemic Index</Text>
            <Text style={[styles.metricSubtitle, { color: giStatus.color }]}>{giStatus.text}</Text>
            <View style={styles.metricValueContainer}>
              <Text style={[styles.metricValue, styles.whiteText]}>{user.dailyGIScore}</Text>
              <Text style={[styles.metricUnit, styles.whiteText]}>score</Text>
            </View>
            <View style={styles.scoreIconContainer}>
              <Ionicons name="nutrition" size={24} color="#FFFFFF" />
            </View>
          </View>
          
          {/* Bottom Right - GL Score */}
          <View style={[styles.metricCard, styles.yellowLight]}>
            <Text style={styles.metricTitle}>Glycemic Load</Text>
            <Text style={[styles.metricSubtitle, { color: glStatus.color }]}>{glStatus.text}</Text>
            <View style={styles.metricValueContainer}>
              <Text style={[styles.metricValue, styles.yellowText]}>{user.dailyGLScore}</Text>
              <Text style={[styles.metricUnit, styles.yellowText]}>score</Text>
            </View>
            <View style={styles.scoreIconContainer}>
              <Ionicons name="restaurant" size={24} color="#FFCC33" />
            </View>
          </View>
        </View>
        
        {/* CGM Data Chart */}
        <GlucoseChart 
          readings={cgmData.map(reading => ({ ...reading, trend: user?.glucoseTrend || 'stable' }))} 
          targetMin={user?.glucoseTargetMin || 70}
          targetMax={user?.glucoseTargetMax || 140}
          latestReading={{
            value: user?.currentGlucose || 100,
            trend: user?.glucoseTrend || 'stable',
            time: cgmData.length > 0 ? cgmData[cgmData.length - 1].time : 'Now'
          }}
        />
        
        {/* Motivational Message */}
        <View style={styles.motivationCard}>
          <View style={styles.motivationHeader}>
            <Text style={styles.motivationTitle}>Today's Motivation</Text>
            <View style={styles.motivationIconContainer}>
              <Ionicons name="flame" size={24} color="#FFCC33" />
            </View>
          </View>
          <Text style={styles.motivationText}>
            "Small daily improvements lead to stunning results over time. Your consistent efforts are building a healthier future!"
          </Text>
          <Text style={styles.motivationSource}>Preventely Health Team</Text>
        </View>
        
        {/* Quick Actions */}
        <View style={styles.categoriesSection}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          
          <View style={styles.categoriesGrid}>
            {/* <TouchableOpacity style={styles.categoryCard} onPress={navigateToFoodTracking}>
              <View style={styles.categoryIconContainer}>
                <Ionicons name="nutrition-outline" size={24} color="#6B5CE5" />
              </View>
              <Text style={styles.categoryLabel}>Log Meal</Text>
            </TouchableOpacity> */}
            
            <TouchableOpacity style={styles.categoryCard} onPress={navigateToReports}>
              <View style={styles.categoryIconContainer}>
                <Ionicons name="stats-chart-outline" size={24} color="#6B5CE5" />
              </View>
              <Text style={styles.categoryLabel}>Reports</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.categoryCard} onPress={navigateToAssistant}>
              <View style={styles.categoryIconContainer}>
                <Ionicons name="chatbubble-ellipses-outline" size={24} color="#6B5CE5" />
              </View>
              <Text style={styles.categoryLabel}>Assistant</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.categoryCard} onPress={navigateToDexcomConnect}>
              <View style={styles.categoryIconContainer}>
                <Ionicons name="pulse-outline" size={24} color="#6B5CE5" />
              </View>
              <Text style={styles.categoryLabel}>Connect CGM</Text>
            </TouchableOpacity>
          </View>
        </View>
        
        {/* Reminders Section */}
        <View style={styles.reminderSection}>
          <View style={styles.sectionTitleContainer}>
            <Text style={styles.sectionTitle}>Today's Reminders</Text>
            <TouchableOpacity onPress={handleViewAllReminders}>
              <Text style={styles.viewAllText}>View All</Text>
            </TouchableOpacity>
          </View>
          
          {reminders.length === 0 ? (
            <Text style={styles.noRemindersText}>No reminders for today</Text>
          ) : reminders.map(reminder => (
            <TouchableOpacity 
              key={reminder.id} 
              style={styles.reminderItem}
              onPress={() => handleToggleReminder(reminder.id)}
            >
              <View style={[styles.reminderIconContainer, reminder.completed && styles.completedReminderIcon]}>
                <Ionicons 
                  name={reminder.completed ? 'checkmark-circle' : reminder.type === 'medication' ? 'medical' : 
                        reminder.type === 'measurement' ? 'pulse' : 
                        reminder.type === 'appointment' ? 'calendar' : 'fitness'} 
                  size={20} 
                  color={reminder.completed ? '#4CAF50' : '#6B5CE5'} 
                />
              </View>
              <View style={styles.reminderContent}>
                <Text style={[styles.reminderTitle, reminder.completed && styles.completedReminderText]}>
                  {reminder.title}
                </Text>
                <Text style={styles.reminderTime}>{reminder.time}</Text>
              </View>
              <Ionicons 
                name={reminder.completed ? 'checkmark-circle' : 'ellipse-outline'} 
                size={24} 
                color={reminder.completed ? '#4CAF50' : '#CCCCCC'} 
              />
            </TouchableOpacity>
          ))}
        </View> 
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({

  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6B5CE5',
  },
  noRemindersText: {
    textAlign: 'center',
    padding: 16,
    color: '#888888',
    fontStyle: 'italic',
  },
  container: {
    flex: 1,
    backgroundColor: '#F1EFFF', // Light purple background like the inspiration image
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  menuButton: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  dateText: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333333',
    textAlign: 'center',
  },
  subDateText: {
    fontSize: 14,
    color: '#6B5CE5', // Purple color for the health journey
    textAlign: 'center',
  },
  
  // Welcome Container Styles
  welcomeContainer: {
    marginBottom: 24,
  },
  welcomeText: {
    fontSize: 22,
    fontWeight: '700',
    color: '#333333',
    marginBottom: 4,
  },
  welcomeSubtext: {
    fontSize: 16,
    color: '#666666',
  },
  
  // Metrics Grid Styles
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  metricCard: {
    width: '48%',
    height: 120,
    borderRadius: 20,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  purpleLight: {
    backgroundColor: '#E9E6FF', // Light purple background
  },
  purpleDark: {
    backgroundColor: '#4A3AFF', // Dark purple background
  },
  purpleMedium: {
    backgroundColor: '#6B5CE5', // Medium purple background
  },
  yellowLight: {
    backgroundColor: '#FFF8E7', // Light yellow background
  },
  metricTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  metricSubtitle: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 4,
  },
  metricValueContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginTop: 8,
  },
  metricValue: {
    fontSize: 32,
    fontWeight: '700',
  },
  metricUnit: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 6,
    marginLeft: 2,
  },
  purpleText: {
    color: '#6B5CE5', // Purple text
  },
  yellowText: {
    color: '#FFCC33', // Yellow text
  },
  whiteText: {
    color: '#FFFFFF', // White text
  },
  trendIconContainer: {
    position: 'absolute',
    bottom: 16,
    right: 16,
  },
  weightIconContainer: {
    position: 'absolute',
    bottom: 16,
    right: 16,
  },
  scoreIconContainer: {
    position: 'absolute',
    bottom: 16,
    right: 16,
  },
  
  // CGM Chart Styles
  chartCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 16,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  chartHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
  },
  chartIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F1EFFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  chartContainer: {
    marginTop: 8,
  },
  cgmChart: {
    height: 120,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    marginBottom: 16,
  },
  cgmDataPoint: {
    alignItems: 'center',
    width: '12%',
  },
  cgmBar: {
    width: 12,
    borderRadius: 6,
    marginBottom: 4,
  },
  cgmTimeLabel: {
    fontSize: 10,
    color: '#666666',
  },
  cgmLegend: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 8,
  },
  cgmLegendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 8,
  },
  cgmLegendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 4,
  },
  cgmLegendText: {
    fontSize: 12,
    color: '#666666',
  },
  
  // Motivation Card Styles
  motivationCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 16,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  motivationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  motivationTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
  },
  motivationIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#FFF8E7',
    justifyContent: 'center',
    alignItems: 'center',
  },
  motivationText: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 22,
    marginBottom: 8,
    fontStyle: 'italic',
  },
  motivationSource: {
    fontSize: 12,
    color: '#6B5CE5',
    marginTop: 8,
    textAlign: 'right',
  },
  
  // Categories Section Styles
  categoriesSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 16,
  },
  categoriesGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  categoryCard: {
    width: '30%',
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  categoryIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#F1EFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryLabel: {
    fontSize: 12,
    color: '#333333',
    textAlign: 'center',
  },
  
  // Reminder Section Styles
  reminderSection: {
    marginBottom: 24,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  viewAllText: {
    fontSize: 14,
    color: '#6B5CE5',
    fontWeight: '500',
  },
  reminderItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  reminderIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F1EFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  completedReminderIcon: {
    backgroundColor: '#E8F5E9',
  },
  reminderContent: {
    flex: 1,
  },
  reminderTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333333',
    marginBottom: 4,
  },
  completedReminderText: {
    textDecorationLine: 'line-through',
    color: '#999999',
  },
  reminderTime: {
    fontSize: 12,
    color: '#666666',
  },
});

export default NewHomeScreen;
