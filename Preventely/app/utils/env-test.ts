import Constants from 'expo-constants';

// Function to check if environment variables are loaded correctly
export const checkEnvironmentVariables = () => {
  console.log('Checking environment variables...');
  
  // Check API_URL from Constants.expoConfig.extra
  const apiUrl = Constants.expoConfig?.extra?.API_URL;
  console.log('API_URL from Constants.expoConfig.extra:', apiUrl);
  
  // Log all available environment variables in Constants.expoConfig.extra
  console.log('All available environment variables:', Constants.expoConfig?.extra);
  
  return {
    apiUrl,
    isConfigured: !!apiUrl,
    allVariables: Constants.expoConfig?.extra
  };
};

export default checkEnvironmentVariables;
